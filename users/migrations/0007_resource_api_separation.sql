-- 资源API分离迁移脚本
-- 第一阶段：表结构创建和数据迁移

-- 1. 为resource表添加permission_id字段
ALTER TABLE `resource`
ADD COLUMN `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID' AFTER `resource_type`,
ADD KEY `idx_resource_permission_id` (`permission_id`),
ADD CONSTRAINT `fk_resource_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE SET NULL;

-- 2. 创建api_resource表
CREATE TABLE `api_resource` (
  `id` bigint NOT NULL COMMENT '分布式ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID',
  `name` varchar(100) NOT NULL COMMENT 'API资源名称',
  `display_name` varchar(100) DEFAULT NULL COMMENT 'API资源显示名称',
  `description` varchar(255) DEFAULT NULL COMMENT 'API资源描述',
  `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID',
  `service_name` varchar(100) NOT NULL COMMENT '应用服务名称',
  `api_method` varchar(10) NOT NULL COMMENT 'HTTP方法: GET, POST, PUT, DELETE, PATCH',
  `path` varchar(255) NOT NULL COMMENT 'API路径',
  `request_type` varchar(50) DEFAULT 'json' COMMENT '请求数据类型',
  `response_type` varchar(50) DEFAULT 'json' COMMENT '响应数据类型',
  `content_type` varchar(100) DEFAULT NULL COMMENT 'Content-Type',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开访问',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_resource_tenant_app` (`tenant_id`, `internal_app_id`),
  KEY `idx_api_resource_permission_id` (`permission_id`),
  KEY `idx_api_resource_service_method` (`service_name`, `api_method`),
  KEY `idx_api_resource_path` (`path`),
  KEY `idx_api_resource_name` (`name`),
  KEY `idx_api_resource_status` (`status`),
  UNIQUE KEY `uk_api_resource_tenant_path_method` (`tenant_id`, `path`, `api_method`),
  CONSTRAINT `fk_api_resource_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API资源表';

-- 3. 创建resource_api_relations表（页面与API关联表）
CREATE TABLE `resource_api_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `resource_id` bigint NOT NULL COMMENT '页面资源ID',
  `api_resource_id` bigint NOT NULL COMMENT 'API资源ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resource_api_resource` (`resource_id`),
  KEY `idx_resource_api_api_id` (`api_resource_id`),
  UNIQUE KEY `uk_resource_api_relation` (`resource_id`, `api_resource_id`),
  CONSTRAINT `fk_resource_api_resource_id` FOREIGN KEY (`resource_id`) REFERENCES `resource` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_resource_api_api_resource_id` FOREIGN KEY (`api_resource_id`) REFERENCES `api_resource` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面与API关联表';

-- 4. 迁移现有API资源到新的api_resource表
INSERT INTO api_resource (
    id, tenant_id, internal_app_id, name, display_name, description,
    service_name, api_method, path, request_type, response_type,
    content_type, is_public, status, created_at, updated_at
)
SELECT
    id, tenant_id, internal_app_id, name, display_name, description,
    service_name, api_method, path, request_type, response_type,
    content_type, is_public, 'active' as status, created_at, updated_at
FROM resource
WHERE resource_type = 'api'
AND service_name IS NOT NULL;

-- 5. 删除原有的API类型资源记录（因为已经迁移到api_resource表）
DELETE FROM resource WHERE resource_type = 'api';

-- 注意：以下步骤将在确认系统运行正常后执行
-- -- 6. 清理resource表中的API相关字段
-- ALTER TABLE `resource`
-- DROP COLUMN `service_name`,
-- DROP COLUMN `request_type`,
-- DROP COLUMN `response_type`,
-- DROP COLUMN `api_method`,
-- DROP COLUMN `content_type`;

-- -- 7. 更新resource_type的约束（移除api类型）
-- -- ALTER TABLE `resource`
-- -- ADD CONSTRAINT `chk_resource_type` CHECK (`resource_type` IN ('menu', 'page', 'button', 'component'));