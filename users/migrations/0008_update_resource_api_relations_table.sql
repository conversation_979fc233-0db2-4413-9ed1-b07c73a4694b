-- 更新 resource_api_relations 表结构
-- 添加设计文档中指定的缺失字段

-- 1. 添加新字段
ALTER TABLE `resource_api_relations`
ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户ID' AFTER `id`,
ADD COLUMN `internal_app_id` bigint NOT NULL DEFAULT 0 COMMENT '内部应用ID' AFTER `tenant_id`, 
ADD COLUMN `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必需' AFTER `api_resource_id`,
ADD COLUMN `description` varchar(255) NOT NULL DEFAULT '' COMMENT '关联关系描述' AFTER `is_required`;

-- 2. 添加索引
ALTER TABLE `resource_api_relations`
ADD KEY `idx_resource_api_tenant` (`tenant_id`, `resource_id`),
ADD KEY `idx_resource_api_app` (`internal_app_id`);

-- 3. 更新唯一约束以包含租户和应用ID
-- 先删除旧的唯一约束
ALTER TABLE `resource_api_relations` DROP INDEX `uk_resource_api_relation`;

-- 添加新的唯一约束
ALTER TABLE `resource_api_relations`
ADD UNIQUE KEY `uk_resource_api_relation` (`tenant_id`, `internal_app_id`, `resource_id`, `api_resource_id`);

-- 4. 为现有数据填充默认值（如果表中已有数据）
-- 注意：这里需要根据实际的资源和API资源数据来填充tenant_id和internal_app_id
-- 这个步骤需要在实际执行时根据业务逻辑进行调整

UPDATE `resource_api_relations` rar
JOIN `resource` r ON rar.resource_id = r.id
JOIN `api_resource` ar ON rar.api_resource_id = ar.id
SET 
    rar.tenant_id = r.tenant_id,
    rar.internal_app_id = r.internal_app_id
WHERE rar.tenant_id = 0 OR rar.internal_app_id = 0;

-- 5. 更新约束，确保tenant_id和internal_app_id不为0（除非有特殊业务需求）
-- ALTER TABLE `resource_api_relations` 
-- ADD CONSTRAINT `chk_tenant_id_not_zero` CHECK (`tenant_id` > 0),
-- ADD CONSTRAINT `chk_internal_app_id_not_zero` CHECK (`internal_app_id` > 0);