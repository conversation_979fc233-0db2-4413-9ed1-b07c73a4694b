# API资源管理接口测试文档

## 服务端API路径配置

基于当前的配置，API资源管理的接口路径如下：

### API前缀配置
- **全局前缀**: `/api/user`
- **服务端口**: `8084` (默认)

### API资源管理接口

#### 1. API资源基本操作
- **创建API资源**: `POST /api/user/api-resource/create`
- **更新API资源**: `POST /api/user/api-resource/update/:id`
- **删除API资源**: `POST /api/user/api-resource/delete/:id`
- **获取API资源**: `GET /api/user/api-resource/get/:id`
- **列表查询API资源**: `POST /api/user/api-resource/list`

#### 2. API资源权限管理
- **分配权限给API资源**: `POST /api/user/api-resource/assign-permission`

#### 3. API资源批量操作
- **批量创建API资源**: `POST /api/user/api-resource/batch-create`
- **批量删除API资源**: `POST /api/user/api-resource/batch-delete`

#### 4. API资源状态管理
- **更新API资源状态**: `POST /api/user/api-resource/update-status/:id`

### 页面与API关联关系接口

#### 1. 基本关联操作
- **创建关联关系**: `POST /api/user/resource-api-relation/create`
- **删除关联关系**: `POST /api/user/resource-api-relation/delete/:resource_id/:api_resource_id`

#### 2. 查询关联关系
- **获取页面关联的API**: `GET /api/user/resource-api-relation/resource/:resource_id/apis`
- **获取API关联的页面**: `GET /api/user/resource-api-relation/api/:api_resource_id/resources`

#### 3. 批量分配
- **批量分配API给页面**: `POST /api/user/resource-api-relation/resource/:resource_id/batch-assign-apis`

#### 4. 复合查询
- **查询页面及其API**: `POST /api/user/resource-api-relation/resources-with-apis`
- **查询API及其页面**: `POST /api/user/resource-api-relation/apis-with-resources`

## 前端集成建议

### 1. API服务配置
```typescript
// api-resource.ts
const API_BASE_URL = 'http://localhost:8084/api/user';

export const apiResourceService = {
  // API资源管理
  createAPIResource: (data: CreateAPIResourceRequest) => 
    post(`${API_BASE_URL}/api-resource/create`, data),
  
  updateAPIResource: (id: number, data: UpdateAPIResourceRequest) => 
    post(`${API_BASE_URL}/api-resource/update/${id}`, data),
  
  deleteAPIResource: (id: number) => 
    post(`${API_BASE_URL}/api-resource/delete/${id}`),
  
  getAPIResource: (id: number) => 
    get(`${API_BASE_URL}/api-resource/get/${id}`),
  
  listAPIResources: (params: ListAPIResourcesRequest) => 
    post(`${API_BASE_URL}/api-resource/list`, params),
  
  // 关联关系管理
  createRelation: (data: CreateResourceAPIRelationRequest) => 
    post(`${API_BASE_URL}/resource-api-relation/create`, data),
  
  getResourceAPIs: (resourceId: number) => 
    get(`${API_BASE_URL}/resource-api-relation/resource/${resourceId}/apis`),
  
  batchAssignAPIs: (resourceId: number, apiResourceIds: number[]) => 
    post(`${API_BASE_URL}/resource-api-relation/resource/${resourceId}/batch-assign-apis`, {
      api_resource_ids: apiResourceIds
    }),
};
```

### 2. 页面组件设计
建议的页面结构：
- **左侧**: 页面资源树（不包含API类型）
- **右侧**: API管理标签页
  - API资源列表
  - 页面与API关联管理
  - API权限配置

## 数据库表结构

### 1. api_resource 表（API资源）
- 包含API的基本信息：服务名、方法、路径等
- 支持权限关联

### 2. resource_api_relations 表（关联关系）
- 管理页面资源与API资源的关联
- 包含租户隔离和必需性标识

## 验证方法

### 1. 启动服务
```bash
cd users && go run cmd/main.go
```

### 2. 测试健康检查
```bash
curl http://localhost:8084/health
```

### 3. 测试API资源创建（需要认证）
```bash
curl -X POST http://localhost:8084/api/user/api-resource/create \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-api",
    "service_name": "user-service",
    "api_method": "GET",
    "path": "/api/test"
  }'
```

## 架构优势

1. **完全分离**: 页面资源和API资源完全独立管理
2. **灵活关联**: 支持多对多的关联关系
3. **租户隔离**: 所有操作都基于租户上下文
4. **权限管控**: API资源可独立配置权限
5. **扩展性**: 支持批量操作和复杂查询