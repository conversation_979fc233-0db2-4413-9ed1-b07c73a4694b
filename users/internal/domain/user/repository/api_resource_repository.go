package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// APIResourceRepository API资源仓储接口
type APIResourceRepository interface {
	// 基本CRUD操作
	Create(ctx context.Context, apiResource *entity.APIResource) error
	Update(ctx context.Context, apiResource *entity.APIResource) error
	Delete(ctx context.Context, id int64, tenantID int64) error
	GetByID(ctx context.Context, id int64) (*entity.APIResource, error)
	GetByIDWithTenant(ctx context.Context, id int64, tenantID int64) (*entity.APIResource, error)

	// 查询操作
	List(ctx context.Context, params *APIResourceQueryParams) ([]*entity.APIResource, error)
	Count(ctx context.Context, params *APIResourceQueryParams) (int64, error)
	GetByPath(ctx context.Context, serviceName, method, path string, tenantID int64) (*entity.APIResource, error)
	GetByServiceAndMethod(ctx context.Context, serviceName, method string, tenantID int64) ([]*entity.APIResource, error)

	// 权限相关操作
	GetByPermissionID(ctx context.Context, permissionID int64, tenantID int64) ([]*entity.APIResource, error)
	AssignPermission(ctx context.Context, apiResourceID, permissionID int64, tenantID int64) error
	RemovePermission(ctx context.Context, apiResourceID int64, tenantID int64) error

	// 批量操作
	BatchCreate(ctx context.Context, apiResources []*entity.APIResource) error
	BatchUpdate(ctx context.Context, apiResources []*entity.APIResource) error
	BatchDelete(ctx context.Context, ids []int64, tenantID int64) error

	// 状态操作
	UpdateStatus(ctx context.Context, id int64, status string, tenantID int64) error
	GetByStatus(ctx context.Context, status string, tenantID int64) ([]*entity.APIResource, error)
}

// APIResourceQueryParams API资源查询参数
type APIResourceQueryParams struct {
	TenantID      int64
	InternalAppID int64
	ServiceName   string
	APIMethod     string
	Path          string
	Status        string
	IsPublic      *bool
	PermissionID  *int64
	Keyword       string // 名称或描述关键词搜索
	Page          int
	PageSize      int
	SortBy        string // 排序字段
	SortOrder     string // 排序顺序: asc, desc
}

// SetDefaults 设置默认值
func (p *APIResourceQueryParams) SetDefaults() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20
	}
	if p.SortBy == "" {
		p.SortBy = "created_at"
	}
	if p.SortOrder == "" {
		p.SortOrder = "desc"
	}
}

// GetOffset 计算分页偏移量
func (p *APIResourceQueryParams) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}