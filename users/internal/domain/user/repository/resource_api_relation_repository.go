package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// ResourceAPIRelationRepository 页面与API关联关系仓储接口
type ResourceAPIRelationRepository interface {
	// 基本CRUD操作
	Create(ctx context.Context, relation *entity.ResourceAPIRelation) error
	Delete(ctx context.Context, resourceID, apiResourceID int64) error
	DeleteByResourceID(ctx context.Context, resourceID int64) error
	DeleteByAPIResourceID(ctx context.Context, apiResourceID int64) error

	// 查询操作
	GetByResourceID(ctx context.Context, resourceID int64) ([]*entity.ResourceAPIRelation, error)
	GetByAPIResourceID(ctx context.Context, apiResourceID int64) ([]*entity.ResourceAPIRelation, error)
	GetByResourceAndAPI(ctx context.Context, resourceID, apiResourceID int64) (*entity.ResourceAPIRelation, error)
	Exists(ctx context.Context, resourceID, apiResourceID int64) (bool, error)

	// 批量操作
	BatchCreate(ctx context.Context, relations []*entity.ResourceAPIRelation) error
	BatchDelete(ctx context.Context, resourceID int64, apiResourceIDs []int64) error
	ReplaceResourceAPIs(ctx context.Context, resourceID int64, apiResourceIDs []int64) error

	// 复杂查询
	GetResourcesWithAPIs(ctx context.Context, params *ResourceAPIQueryParams) ([]*ResourceWithAPIs, error)
	GetAPIsWithResources(ctx context.Context, params *APIResourceWithResourcesQueryParams) ([]*APIResourceWithResources, error)
}

// ResourceWithAPIs 页面资源及其关联的API
type ResourceWithAPIs struct {
	Resource     *entity.Resource     `json:"resource"`
	APIResources []*entity.APIResource `json:"api_resources"`
	Relations    []*entity.ResourceAPIRelation `json:"relations"`
}

// APIResourceWithResources API资源及其关联的页面资源
type APIResourceWithResources struct {
	APIResource *entity.APIResource `json:"api_resource"`
	Resources   []*entity.Resource  `json:"resources"`
	Relations   []*entity.ResourceAPIRelation `json:"relations"`
}

// ResourceAPIQueryParams 页面与API关联查询参数
type ResourceAPIQueryParams struct {
	TenantID      int64
	InternalAppID int64
	ResourceID    *int64
	ResourceType  string
	Page          int
	PageSize      int
}

// SetDefaults 设置默认值
func (p *ResourceAPIQueryParams) SetDefaults() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20
	}
}

// GetOffset 计算分页偏移量
func (p *ResourceAPIQueryParams) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// APIResourceWithResourcesQueryParams API与页面关联查询参数
type APIResourceWithResourcesQueryParams struct {
	TenantID       int64
	InternalAppID  int64
	APIResourceID  *int64
	ServiceName    string
	APIMethod      string
	Page           int
	PageSize       int
}

// SetDefaults 设置默认值
func (p *APIResourceWithResourcesQueryParams) SetDefaults() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20
	}
}

// GetOffset 计算分页偏移量
func (p *APIResourceWithResourcesQueryParams) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}