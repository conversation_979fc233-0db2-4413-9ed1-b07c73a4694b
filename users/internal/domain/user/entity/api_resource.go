package entity

import (
	"time"
)

// APIResource API资源实体
type APIResource struct {
	ID            int64
	TenantID      int64
	InternalAppID int64
	Name          string
	DisplayName   string
	Description   string
	PermissionID  *int64 // 关联的权限ID
	ServiceName   string
	APIMethod     string
	Path          string
	RequestType   string
	ResponseType  string
	ContentType   string
	IsPublic      bool
	Status        string
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}

// TableName 指定表名
func (a *APIResource) TableName() string {
	return "api_resource"
}

// IsActive 检查API资源是否激活
func (a *APIResource) IsActive() bool {
	return a.Status == "active"
}

// IsPublicResource 检查是否为公开API资源
func (a *APIResource) IsPublicResource() bool {
	return a.IsPublic
}

// HasPermission 检查是否有关联权限
func (a *APIResource) HasPermission() bool {
	return a.PermissionID != nil
}

// GetPermissionID 获取权限ID
func (a *APIResource) GetPermissionID() *int64 {
	return a.PermissionID
}

// GetFullPath 获取完整API路径
func (a *APIResource) GetFullPath() string {
	if a.Path == "" {
		return a.Name
	}
	return a.Path
}

// GetMethodPath 获取方法和路径组合
func (a *APIResource) GetMethodPath() string {
	return a.APIMethod + " " + a.GetFullPath()
}

// SetStatus 设置状态
func (a *APIResource) SetStatus(status string) {
	a.Status = status
}

// SetPublicAccess 设置公开访问
func (a *APIResource) SetPublicAccess(isPublic bool) {
	a.IsPublic = isPublic
}

// SetPermission 设置权限ID
func (a *APIResource) SetPermission(permissionID *int64) {
	a.PermissionID = permissionID
}

// Validate 验证API资源数据
func (a *APIResource) Validate() error {
	if a.Name == "" {
		return NewValidationError("API资源名称不能为空")
	}
	if a.ServiceName == "" {
		return NewValidationError("服务名称不能为空")
	}
	if a.APIMethod == "" {
		return NewValidationError("API方法不能为空")
	}
	if a.Path == "" {
		return NewValidationError("API路径不能为空")
	}
	return nil
}

// ValidationError 验证错误
type ValidationError struct {
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// NewValidationError 创建验证错误
func NewValidationError(message string) error {
	return &ValidationError{Message: message}
}