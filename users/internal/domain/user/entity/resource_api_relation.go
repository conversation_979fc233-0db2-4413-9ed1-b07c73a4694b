package entity

import (
	"time"
)

// ResourceAPIRelation 页面与API关联实体
type ResourceAPIRelation struct {
	ID            int64
	TenantID      int64    // 租户ID
	InternalAppID int64    // 内部应用ID  
	ResourceID    int64    // 页面资源ID
	APIResourceID int64    // API资源ID
	IsRequired    bool     // 是否必需
	Description   string   // 关联关系描述
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// TableName 指定表名
func (r *ResourceAPIRelation) TableName() string {
	return "resource_api_relations"
}

// Validate 验证关联关系数据
func (r *ResourceAPIRelation) Validate() error {
	if r.TenantID <= 0 {
		return NewValidationError("租户ID不能为空")
	}
	if r.InternalAppID <= 0 {
		return NewValidationError("内部应用ID不能为空")
	}
	if r.ResourceID <= 0 {
		return NewValidationError("页面资源ID不能为空")
	}
	if r.APIResourceID <= 0 {
		return NewValidationError("API资源ID不能为空")
	}
	return nil
}

// GetResourceID 获取页面资源ID
func (r *ResourceAPIRelation) GetResourceID() int64 {
	return r.ResourceID
}

// GetAPIResourceID 获取API资源ID
func (r *ResourceAPIRelation) GetAPIResourceID() int64 {
	return r.APIResourceID
}

// SetResourceID 设置页面资源ID
func (r *ResourceAPIRelation) SetResourceID(resourceID int64) {
	r.ResourceID = resourceID
}

// SetAPIResourceID 设置API资源ID
func (r *ResourceAPIRelation) SetAPIResourceID(apiResourceID int64) {
	r.APIResourceID = apiResourceID
}

// GetTenantID 获取租户ID
func (r *ResourceAPIRelation) GetTenantID() int64 {
	return r.TenantID
}

// SetTenantID 设置租户ID
func (r *ResourceAPIRelation) SetTenantID(tenantID int64) {
	r.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (r *ResourceAPIRelation) GetInternalAppID() int64 {
	return r.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (r *ResourceAPIRelation) SetInternalAppID(internalAppID int64) {
	r.InternalAppID = internalAppID
}

// IsRequiredRelation 检查关联关系是否必需
func (r *ResourceAPIRelation) IsRequiredRelation() bool {
	return r.IsRequired
}

// SetRequired 设置是否必需
func (r *ResourceAPIRelation) SetRequired(required bool) {
	r.IsRequired = required
}

// GetDescription 获取关联关系描述
func (r *ResourceAPIRelation) GetDescription() string {
	return r.Description
}

// SetDescription 设置关联关系描述
func (r *ResourceAPIRelation) SetDescription(description string) {
	r.Description = description
}