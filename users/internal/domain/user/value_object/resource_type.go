package value_object

// ResourceType 资源类型枚举
type ResourceType string

const (
	ResourceTypePage      ResourceType = "page"      // 页面资源
	ResourceTypeButton    ResourceType = "button"    // 按钮资源
	ResourceTypeMenu      ResourceType = "menu"      // 菜单资源
	ResourceTypeComponent ResourceType = "component" // 组件资源
	ResourceTypeData      ResourceType = "data"      // 数据资源
	ResourceTypeSystem    ResourceType = "system"    // 系统资源
	ResourceTypeFunction  ResourceType = "function"  // 功能资源
)

// String 实现Stringer接口
func (rt ResourceType) String() string {
	return string(rt)
}

// IsValid 检查资源类型是否有效
func (rt ResourceType) IsValid() bool {
	switch rt {
	case ResourceTypePage, ResourceTypeButton, ResourceTypeMenu,
		 ResourceTypeComponent, ResourceTypeData, ResourceTypeSystem, ResourceTypeFunction:
		return true
	default:
		return false
	}
}

// GetDisplayName 获取资源类型显示名称
func (rt ResourceType) GetDisplayName() string {
	switch rt {
	case ResourceTypePage:
		return "页面"
	case ResourceTypeButton:
		return "按钮"
	case ResourceTypeMenu:
		return "菜单"
	case ResourceTypeComponent:
		return "组件"
	case ResourceTypeData:
		return "数据"
	case ResourceTypeSystem:
		return "系统"
	case ResourceTypeFunction:
		return "功能"
	default:
		return "未知"
	}
}

// GetAllResourceTypes 获取所有有效的资源类型
func GetAllResourceTypes() []ResourceType {
	return []ResourceType{
		ResourceTypePage,
		ResourceTypeButton,
		ResourceTypeMenu,
		ResourceTypeComponent,
		ResourceTypeData,
		ResourceTypeSystem,
		ResourceTypeFunction,
	}
}

// GetAllResourceTypeStrings 获取所有有效的资源类型字符串
func GetAllResourceTypeStrings() []string {
	types := GetAllResourceTypes()
	result := make([]string, len(types))
	for i, t := range types {
		result[i] = string(t)
	}
	return result
} 