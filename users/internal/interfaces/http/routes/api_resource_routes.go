package routes

import (
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	"github.com/gin-gonic/gin"
)

// SetupAPIResourceRoutes 设置API资源相关路由
func SetupAPIResourceRoutes(r *gin.RouterGroup, apiResourceHandler *handlers.APIResourceHandler) {
	apiResourceGroup := r.Group("/api-resource")
	{
		// 基本CRUD操作
		apiResourceGroup.POST("/create", apiResourceHandler.CreateAPIResource)
		apiResourceGroup.POST("/update", apiResourceHandler.UpdateAPIResource)
		apiResourceGroup.POST("/delete", apiResourceHandler.DeleteAPIResource)
		apiResourceGroup.GET("/get", apiResourceHandler.GetAPIResource)
		apiResourceGroup.POST("/list", apiResourceHandler.ListAPIResources)

		// 权限管理
		apiResourceGroup.POST("/assign-permission", apiResourceHandler.AssignPermissionToAPIResource)

		// 批量操作
		apiResourceGroup.POST("/batch-create", apiResourceHandler.BatchCreateAPIResources)
		apiResourceGroup.POST("/batch-delete", apiResourceHandler.BatchDeleteAPIResources)

		// 状态管理
		apiResourceGroup.POST("/update-status", apiResourceHandler.UpdateAPIResourceStatus)
	}
}

// SetupResourceAPIRelationRoutes 设置页面与API关联关系路由
func SetupResourceAPIRelationRoutes(r *gin.RouterGroup, relationHandler *handlers.ResourceAPIRelationHandler) {
	relationGroup := r.Group("/resource-api-relation")
	{
		// 基本关联操作
		relationGroup.POST("/create", relationHandler.CreateRelation)
		relationGroup.POST("/delete", relationHandler.DeleteRelation)

		// 查询关联关系
		relationGroup.GET("/get-resource-apis", relationHandler.GetResourceAPIs)
		relationGroup.GET("/get-api-resources", relationHandler.GetAPIResources)

		// 批量分配
		relationGroup.POST("/batch-assign-apis", relationHandler.BatchAssignAPIsToResource)

		// 复合查询
		relationGroup.POST("/list-resources-with-apis", relationHandler.ListResourcesWithAPIs)
		relationGroup.POST("/list-apis-with-resources", relationHandler.ListAPIsWithResources)
	}
}