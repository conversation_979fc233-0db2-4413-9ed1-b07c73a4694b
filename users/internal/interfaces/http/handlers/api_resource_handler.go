package handlers

import (
	"net/http"
	"strconv"

	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/application/user/service"
	"github.com/gin-gonic/gin"
)

// APIResourceHandler API资源处理器
type APIResourceHandler struct {
	apiResourceService *service.APIResourceApplicationService
}

// NewAPIResourceHandler 创建API资源处理器实例
func NewAPIResourceHandler(apiResourceService *service.APIResourceApplicationService) *APIResourceHandler {
	return &APIResourceHandler{
		apiResourceService: apiResourceService,
	}
}

// CreateAPIResource 创建API资源
func (h *APIResourceHandler) CreateAPIResource(c *gin.Context) {
	var req dto.CreateAPIResourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	apiResource, err := h.apiResourceService.CreateAPIResource(c.Request.Context(), &req)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed)
		return
	}
	commonResponse.Success(c, apiResource)
}

// UpdateAPIResource 更新API资源
func (h *APIResourceHandler) UpdateAPIResource(c *gin.Context) {
	var req dto.UpdateAPIResourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从URL参数获取ID
	idStr := c.Param("id")
	if idStr != "" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的资源ID")
			return
		}
		req.ID = id
	}

	if err := h.apiResourceService.UpdateAPIResource(c.Request.Context(), &req); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "更新API资源失败")
		return
	}
	commonResponse.Success(c, true)
}

// DeleteAPIResource 删除API资源
func (h *APIResourceHandler) DeleteAPIResource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的资源ID")
		return
	}

	if err := h.apiResourceService.DeleteAPIResource(c.Request.Context(), id); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "删除API资源失败")
		return
	}
	commonResponse.Success(c, true)
}

// GetAPIResource 获取API资源详情
func (h *APIResourceHandler) GetAPIResource(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的资源ID")
		return
	}

	apiResource, err := h.apiResourceService.GetAPIResource(c.Request.Context(), id)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "获取API资源失败")
		return
	}

	commonResponse.Success(c, apiResource)
}

// ListAPIResources 列表查询API资源
func (h *APIResourceHandler) ListAPIResources(c *gin.Context) {
	var req dto.ListAPIResourcesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	apiResources, err := h.apiResourceService.ListAPIResources(c.Request.Context(), &req)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "查询API资源列表失败")
		return
	}

	// 使用标准分页响应，符合项目规范
	commonResponse.Paginated(c, apiResources.Items, apiResources.Page, apiResources.PageSize, apiResources.Total)
}

// AssignPermissionToAPIResource 分配权限给API资源
func (h *APIResourceHandler) AssignPermissionToAPIResource(c *gin.Context) {
	var req dto.AssignPermissionToAPIResourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	if err := h.apiResourceService.AssignPermissionToAPIResource(c.Request.Context(), &req); err != nil {
		commonResponse.Error(c, http.StatusInternalServerError, "分配权限失败", err.Error())
		return
	}
	commonResponse.Success(c, true)
}

// BatchCreateAPIResources 批量创建API资源
func (h *APIResourceHandler) BatchCreateAPIResources(c *gin.Context) {
	var req dto.BatchCreateAPIResourcesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	apiResources, err := h.apiResourceService.BatchCreateAPIResources(c.Request.Context(), &req)
	if err != nil {
		commonResponse.Error(c, http.StatusInternalServerError, "批量创建API资源失败", err.Error())
		return
	}
	commonResponse.Success(c, apiResources)
}

// BatchDeleteAPIResources 批量删除API资源
func (h *APIResourceHandler) BatchDeleteAPIResources(c *gin.Context) {
	var req dto.BatchDeleteAPIResourcesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	if err := h.apiResourceService.BatchDeleteAPIResources(c.Request.Context(), &req); err != nil {
		commonResponse.Error(c, http.StatusInternalServerError, "批量删除API资源失败", err.Error())
		return
	}
	commonResponse.Success(c, true)
}

// UpdateAPIResourceStatus 更新API资源状态
func (h *APIResourceHandler) UpdateAPIResourceStatus(c *gin.Context) {
	var req dto.UpdateAPIResourceStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从URL参数获取ID
	idStr := c.Param("id")
	if idStr != "" {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的资源ID")
			return
		}
		req.ID = id
	}

	if err := h.apiResourceService.UpdateAPIResourceStatus(c.Request.Context(), &req); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "更新API资源状态失败")
		return
	}
	commonResponse.Success(c, true)
}
