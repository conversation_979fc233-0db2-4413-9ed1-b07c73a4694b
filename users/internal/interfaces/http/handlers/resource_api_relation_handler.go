package handlers

import (
	"strconv"

	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/application/user/service"
	"github.com/gin-gonic/gin"
)

// ResourceAPIRelationHandler 页面与API关联关系处理器
type ResourceAPIRelationHandler struct {
	relationService *service.ResourceAPIRelationApplicationService
}

// NewResourceAPIRelationHandler 创建页面与API关联关系处理器实例
func NewResourceAPIRelationHandler(relationService *service.ResourceAPIRelationApplicationService) *ResourceAPIRelationHandler {
	return &ResourceAPIRelationHandler{
		relationService: relationService,
	}
}

// CreateRelation 创建页面与API关联关系
func (h *ResourceAPIRelationHandler) CreateRelation(c *gin.Context) {
	var req dto.CreateResourceAPIRelationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "请求参数错误")
		return
	}

	relation, err := h.relationService.CreateRelation(c.Request.Context(), &req)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "创建关联关系失败")
		return
	}

	commonResponse.Success(c, relation)
}

// DeleteRelation 删除页面与API关联关系
func (h *ResourceAPIRelationHandler) DeleteRelation(c *gin.Context) {
	resourceIDStr := c.Param("resource_id")
	resourceID, err := strconv.ParseInt(resourceIDStr, 10, 64)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的页面资源ID")
		return
	}

	apiResourceIDStr := c.Param("api_resource_id")
	apiResourceID, err := strconv.ParseInt(apiResourceIDStr, 10, 64)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的API资源ID")
		return
	}

	if err := h.relationService.DeleteRelation(c.Request.Context(), resourceID, apiResourceID); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "删除关联关系失败")
		return
	}

	commonResponse.Success(c, true)
}

// GetResourceAPIs 获取页面资源关联的API列表
func (h *ResourceAPIRelationHandler) GetResourceAPIs(c *gin.Context) {
	resourceIDStr := c.Param("resource_id")
	resourceID, err := strconv.ParseInt(resourceIDStr, 10, 64)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的页面资源ID")
		return
	}

	apiResources, err := h.relationService.GetResourceAPIs(c.Request.Context(), resourceID)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "获取页面关联的API失败")
		return
	}

	commonResponse.Success(c, apiResources)
}

// GetAPIResources 获取API资源关联的页面列表
func (h *ResourceAPIRelationHandler) GetAPIResources(c *gin.Context) {
	apiResourceIDStr := c.Param("api_resource_id")
	apiResourceID, err := strconv.ParseInt(apiResourceIDStr, 10, 64)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的API资源ID")
		return
	}

	resources, err := h.relationService.GetAPIResources(c.Request.Context(), apiResourceID)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "获取API关联的页面失败")
		return
	}

	commonResponse.Success(c, resources)
}

// BatchAssignAPIsToResource 批量分配API给页面资源
func (h *ResourceAPIRelationHandler) BatchAssignAPIsToResource(c *gin.Context) {
	var req dto.BatchAssignAPIsToResourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "请求参数错误")
		return
	}

	// 从URL参数获取页面资源ID
	resourceIDStr := c.Param("resource_id")
	if resourceIDStr != "" {
		resourceID, err := strconv.ParseInt(resourceIDStr, 10, 64)
		if err != nil {
			commonResponse.Error(c, commonResponse.CodeOperationFailed, "无效的页面资源ID")
			return
		}
		req.ResourceID = resourceID
	}

	if err := h.relationService.BatchAssignAPIsToResource(c.Request.Context(), &req); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "批量分配API给页面资源失败")
		return
	}

	commonResponse.Success(c, gin.H{"message": "批量分配API给页面资源成功"})
}

// ListResourcesWithAPIs 列表查询页面资源及其关联的API
func (h *ResourceAPIRelationHandler) ListResourcesWithAPIs(c *gin.Context) {
	var req dto.ListResourcesWithAPIsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "请求参数错误")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	resourcesWithAPIs, err := h.relationService.ListResourcesWithAPIs(c.Request.Context(), &req)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "查询页面资源及其API失败")
		return
	}

	// 使用标准分页响应，符合项目规范
	commonResponse.Paginated(c, resourcesWithAPIs.Items, resourcesWithAPIs.Page, resourcesWithAPIs.PageSize, resourcesWithAPIs.Total)
}

// ListAPIsWithResources 列表查询API资源及其关联的页面
func (h *ResourceAPIRelationHandler) ListAPIsWithResources(c *gin.Context) {
	var req dto.ListAPIsWithResourcesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "请求参数错误")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	apisWithResources, err := h.relationService.ListAPIsWithResources(c.Request.Context(), &req)
	if err != nil {
		commonResponse.Error(c, commonResponse.CodeOperationFailed, "查询API资源及其页面失败")
		return
	}

	// 使用标准分页响应，符合项目规范
	commonResponse.Paginated(c, apisWithResources.Items, apisWithResources.Page, apisWithResources.PageSize, apisWithResources.Total)
}
