package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// APIResourceModel API资源数据模型
type APIResourceModel struct {
	ID            int64      `gorm:"primaryKey"`
	TenantID      int64      `gorm:"not null;index"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能"`
	Name          string     `gorm:"not null;size:100"`
	DisplayName   string     `gorm:"size:100"`
	Description   string     `gorm:"size:255"`
	PermissionID  *int64     `gorm:"index;comment:关联的权限ID"`
	ServiceName   string     `gorm:"not null;size:100"`
	APIMethod     string     `gorm:"not null;size:10"`
	Path          string     `gorm:"not null;size:255"`
	RequestType   string     `gorm:"size:50;default:json"`
	ResponseType  string     `gorm:"size:50;default:json"`
	ContentType   string     `gorm:"size:100"`
	IsPublic      bool       `gorm:"default:false"`
	Status        string     `gorm:"not null;size:20;default:active"`
	CreatedAt     time.Time  `gorm:"autoCreateTime"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime"`
	DeletedAt     *time.Time `gorm:"index"`

	// 参数模式控制（不持久化）
	allowParameterMode bool
}

// TableName 指定表名
func (APIResourceModel) TableName() string {
	return "api_resource"
}

// GetTenantID 获取租户ID
func (a *APIResourceModel) GetTenantID() int64 {
	return a.TenantID
}

// SetTenantID 设置租户ID
func (a *APIResourceModel) SetTenantID(tenantID int64) {
	a.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (a *APIResourceModel) GetInternalAppID() int64 {
	return a.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (a *APIResourceModel) SetInternalAppID(appID int64) {
	a.InternalAppID = appID
}

// IsAllowParameterMode 是否允许参数模式（默认false，严格使用context）
func (a *APIResourceModel) IsAllowParameterMode() bool {
	return a.allowParameterMode
}

// SetAllowParameterMode 设置参数模式
func (a *APIResourceModel) SetAllowParameterMode(allow bool) {
	a.allowParameterMode = allow
}

// EnableParameterMode 临时启用参数模式（用于特殊场景）
func (a *APIResourceModel) EnableParameterMode() {
	a.allowParameterMode = true
}

// DisableParameterMode 禁用参数模式（恢复严格模式）
func (a *APIResourceModel) DisableParameterMode() {
	a.allowParameterMode = false
}

// BeforeCreate GORM创建前钩子
func (a *APIResourceModel) BeforeCreate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (a *APIResourceModel) BeforeUpdate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (a *APIResourceModel) BeforeQuery(tx *gorm.DB) error {
	model.AutoFilterTenantAndApp(tx)
	return nil
}

// ToDomainEntity 转换为领域实体
func (a *APIResourceModel) ToDomainEntity() *entity.APIResource {
	apiResource := &entity.APIResource{
		ID:            a.ID,
		TenantID:      a.TenantID,
		InternalAppID: a.InternalAppID,
		Name:          a.Name,
		DisplayName:   a.DisplayName,
		Description:   a.Description,
		PermissionID:  a.PermissionID,
		ServiceName:   a.ServiceName,
		APIMethod:     a.APIMethod,
		Path:          a.Path,
		RequestType:   a.RequestType,
		ResponseType:  a.ResponseType,
		ContentType:   a.ContentType,
		IsPublic:      a.IsPublic,
		Status:        a.Status,
		CreatedAt:     a.CreatedAt,
		UpdatedAt:     a.UpdatedAt,
		DeletedAt:     a.DeletedAt,
	}

	return apiResource
}

// FromDomainEntity 从领域实体创建数据模型
func (a *APIResourceModel) FromDomainEntity(apiResource *entity.APIResource) {
	a.ID = apiResource.ID
	a.TenantID = apiResource.TenantID
	a.InternalAppID = apiResource.InternalAppID
	a.Name = apiResource.Name
	a.DisplayName = apiResource.DisplayName
	a.Description = apiResource.Description
	a.PermissionID = apiResource.PermissionID
	a.ServiceName = apiResource.ServiceName
	a.APIMethod = apiResource.APIMethod
	a.Path = apiResource.Path
	a.RequestType = apiResource.RequestType
	a.ResponseType = apiResource.ResponseType
	a.ContentType = apiResource.ContentType
	a.IsPublic = apiResource.IsPublic
	a.Status = apiResource.Status
	a.CreatedAt = apiResource.CreatedAt
	a.UpdatedAt = apiResource.UpdatedAt
	a.DeletedAt = apiResource.DeletedAt
}

// NewAPIResourceModelFromDomain 从领域实体创建数据模型
func NewAPIResourceModelFromDomain(apiResource *entity.APIResource) *APIResourceModel {
	model := &APIResourceModel{}
	model.FromDomainEntity(apiResource)
	return model
}

// NewAPIResourceModelFromDomainWithParameterMode 从领域实体创建数据模型，可选择是否启用参数模式
func NewAPIResourceModelFromDomainWithParameterMode(apiResource *entity.APIResource, enableParameterMode bool) *APIResourceModel {
	model := &APIResourceModel{}
	model.FromDomainEntity(apiResource)
	if enableParameterMode {
		model.EnableParameterMode()
	}
	return model
}