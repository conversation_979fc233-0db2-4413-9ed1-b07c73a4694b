package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gorm.io/gorm"
)

// ResourceAPIRelationModel 页面与API关联数据模型
type ResourceAPIRelationModel struct {
	ID            int64     `gorm:"primaryKey;autoIncrement"`
	TenantID      int64     `gorm:"not null;index"`
	InternalAppID int64     `gorm:"not null;index"`
	ResourceID    int64     `gorm:"not null;index"`
	APIResourceID int64     `gorm:"not null;index"`
	IsRequired    bool      `gorm:"not null;default:false"`
	Description   string    `gorm:"type:varchar(255);default:''"`
	CreatedAt     time.Time `gorm:"autoCreateTime"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (ResourceAPIRelationModel) TableName() string {
	return "resource_api_relations"
}

// BeforeCreate GORM创建前钩子
func (r *ResourceAPIRelationModel) BeforeCreate(tx *gorm.DB) error {
	// 关联表不需要自动填充tenant_id和app_id，因为关联的资源已经有这些信息
	return nil
}

// BeforeUpdate GORM更新前钩子
func (r *ResourceAPIRelationModel) BeforeUpdate(tx *gorm.DB) error {
	return nil
}

// ToDomainEntity 转换为领域实体
func (r *ResourceAPIRelationModel) ToDomainEntity() *entity.ResourceAPIRelation {
	relation := &entity.ResourceAPIRelation{
		ID:            r.ID,
		TenantID:      r.TenantID,
		InternalAppID: r.InternalAppID,
		ResourceID:    r.ResourceID,
		APIResourceID: r.APIResourceID,
		IsRequired:    r.IsRequired,
		Description:   r.Description,
		CreatedAt:     r.CreatedAt,
		UpdatedAt:     r.UpdatedAt,
	}

	return relation
}

// FromDomainEntity 从领域实体创建数据模型
func (r *ResourceAPIRelationModel) FromDomainEntity(relation *entity.ResourceAPIRelation) {
	r.ID = relation.ID
	r.TenantID = relation.TenantID
	r.InternalAppID = relation.InternalAppID
	r.ResourceID = relation.ResourceID
	r.APIResourceID = relation.APIResourceID
	r.IsRequired = relation.IsRequired
	r.Description = relation.Description
	r.CreatedAt = relation.CreatedAt
	r.UpdatedAt = relation.UpdatedAt
}

// NewResourceAPIRelationModelFromDomain 从领域实体创建数据模型
func NewResourceAPIRelationModelFromDomain(relation *entity.ResourceAPIRelation) *ResourceAPIRelationModel {
	model := &ResourceAPIRelationModel{}
	model.FromDomainEntity(relation)
	return model
}