package persistence

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/models"
	"gorm.io/gorm"
)

// ResourceAPIRelationRepositoryImpl 页面与API关联关系仓储实现
type ResourceAPIRelationRepositoryImpl struct {
	*BaseRepository[models.ResourceAPIRelationModel]
	resourceRepo    repository.ResourceRepository
	apiResourceRepo repository.APIResourceRepository
}

// NewResourceAPIRelationRepository 创建页面与API关联关系仓储实例
func NewResourceAPIRelationRepository(db *gorm.DB, logger logiface.Logger, resourceRepo repository.ResourceRepository, apiResourceRepo repository.APIResourceRepository) repository.ResourceAPIRelationRepository {
	return &ResourceAPIRelationRepositoryImpl{
		BaseRepository:  NewBaseRepository[models.ResourceAPIRelationModel](db, logger),
		resourceRepo:    resourceRepo,
		apiResourceRepo: apiResourceRepo,
	}
}

// Create 创建关联关系
func (r *ResourceAPIRelationRepositoryImpl) Create(ctx context.Context, relation *entity.ResourceAPIRelation) error {
	model := models.NewResourceAPIRelationModelFromDomain(relation)
	
	if err := r.GetDB().WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("创建页面与API关联关系失败: %w", err)
	}
	
	relation.ID = model.ID
	return nil
}

// Delete 删除关联关系
func (r *ResourceAPIRelationRepositoryImpl) Delete(ctx context.Context, resourceID, apiResourceID int64) error {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}
	
	result := r.GetDB().WithContext(ctx).Where("resource_id = ? AND api_resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
		resourceID, apiResourceID, appInfo.TenantID, appInfo.InternalAppId).Delete(&models.ResourceAPIRelationModel{})
	
	if result.Error != nil {
		return fmt.Errorf("删除页面与API关联关系失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("关联关系不存在")
	}
	
	return nil
}

// DeleteByResourceID 根据页面资源ID删除所有关联关系
func (r *ResourceAPIRelationRepositoryImpl) DeleteByResourceID(ctx context.Context, resourceID int64) error {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}
	
	if err := r.GetDB().WithContext(ctx).Where("resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
		resourceID, appInfo.TenantID, appInfo.InternalAppId).
		Delete(&models.ResourceAPIRelationModel{}).Error; err != nil {
		return fmt.Errorf("删除页面关联的API失败: %w", err)
	}
	
	return nil
}

// DeleteByAPIResourceID 根据API资源ID删除所有关联关系
func (r *ResourceAPIRelationRepositoryImpl) DeleteByAPIResourceID(ctx context.Context, apiResourceID int64) error {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}
	
	if err := r.GetDB().WithContext(ctx).Where("api_resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
		apiResourceID, appInfo.TenantID, appInfo.InternalAppId).
		Delete(&models.ResourceAPIRelationModel{}).Error; err != nil {
		return fmt.Errorf("删除API关联的页面失败: %w", err)
	}
	
	return nil
}

// GetByResourceID 根据页面资源ID获取关联关系
func (r *ResourceAPIRelationRepositoryImpl) GetByResourceID(ctx context.Context, resourceID int64) ([]*entity.ResourceAPIRelation, error) {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}
	
	var modelList []models.ResourceAPIRelationModel
	
	if err := r.GetDB().WithContext(ctx).Where("resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
		resourceID, appInfo.TenantID, appInfo.InternalAppId).
		Find(&modelList).Error; err != nil {
		return nil, fmt.Errorf("获取页面关联的API失败: %w", err)
	}
	
	entities := make([]*entity.ResourceAPIRelation, len(modelList))
	for i, model := range modelList {
		entities[i] = model.ToDomainEntity()
	}
	
	return entities, nil
}

// GetByAPIResourceID 根据API资源ID获取关联关系
func (r *ResourceAPIRelationRepositoryImpl) GetByAPIResourceID(ctx context.Context, apiResourceID int64) ([]*entity.ResourceAPIRelation, error) {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}
	
	var modelList []models.ResourceAPIRelationModel
	
	if err := r.GetDB().WithContext(ctx).Where("api_resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
		apiResourceID, appInfo.TenantID, appInfo.InternalAppId).
		Find(&modelList).Error; err != nil {
		return nil, fmt.Errorf("获取API关联的页面失败: %w", err)
	}
	
	entities := make([]*entity.ResourceAPIRelation, len(modelList))
	for i, model := range modelList {
		entities[i] = model.ToDomainEntity()
	}
	
	return entities, nil
}

// GetByResourceAndAPI 根据页面资源ID和API资源ID获取关联关系
func (r *ResourceAPIRelationRepositoryImpl) GetByResourceAndAPI(ctx context.Context, resourceID, apiResourceID int64) (*entity.ResourceAPIRelation, error) {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}
	
	var model models.ResourceAPIRelationModel
	
	if err := r.GetDB().WithContext(ctx).Where("resource_id = ? AND api_resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
		resourceID, apiResourceID, appInfo.TenantID, appInfo.InternalAppId).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("关联关系不存在")
		}
		return nil, fmt.Errorf("获取关联关系失败: %w", err)
	}
	
	return model.ToDomainEntity(), nil
}

// Exists 检查关联关系是否存在
func (r *ResourceAPIRelationRepositoryImpl) Exists(ctx context.Context, resourceID, apiResourceID int64) (bool, error) {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return false, fmt.Errorf("应用上下文不存在")
	}
	
	var count int64
	
	if err := r.GetDB().WithContext(ctx).Model(&models.ResourceAPIRelationModel{}).
		Where("resource_id = ? AND api_resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
			resourceID, apiResourceID, appInfo.TenantID, appInfo.InternalAppId).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("检查关联关系失败: %w", err)
	}
	
	return count > 0, nil
}

// BatchCreate 批量创建关联关系
func (r *ResourceAPIRelationRepositoryImpl) BatchCreate(ctx context.Context, relations []*entity.ResourceAPIRelation) error {
	if len(relations) == 0 {
		return nil
	}
	
	modelList := make([]*models.ResourceAPIRelationModel, len(relations))
	for i, relation := range relations {
		modelList[i] = models.NewResourceAPIRelationModelFromDomain(relation)
	}
	
	if err := r.GetDB().WithContext(ctx).Create(modelList).Error; err != nil {
		return fmt.Errorf("批量创建关联关系失败: %w", err)
	}
	
	// 更新实体ID
	for i, model := range modelList {
		relations[i].ID = model.ID
	}
	
	return nil
}

// BatchDelete 批量删除关联关系
func (r *ResourceAPIRelationRepositoryImpl) BatchDelete(ctx context.Context, resourceID int64, apiResourceIDs []int64) error {
	if len(apiResourceIDs) == 0 {
		return nil
	}
	
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}
	
	if err := r.GetDB().WithContext(ctx).Where("resource_id = ? AND api_resource_id IN ? AND tenant_id = ? AND internal_app_id = ?", 
		resourceID, apiResourceIDs, appInfo.TenantID, appInfo.InternalAppId).Delete(&models.ResourceAPIRelationModel{}).Error; err != nil {
		return fmt.Errorf("批量删除关联关系失败: %w", err)
	}
	
	return nil
}

// ReplaceResourceAPIs 替换页面资源的所有API关联
func (r *ResourceAPIRelationRepositoryImpl) ReplaceResourceAPIs(ctx context.Context, resourceID int64, apiResourceIDs []int64) error {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}
	
	return r.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除现有关联
		if err := tx.Where("resource_id = ? AND tenant_id = ? AND internal_app_id = ?", 
			resourceID, appInfo.TenantID, appInfo.InternalAppId).
			Delete(&models.ResourceAPIRelationModel{}).Error; err != nil {
			return fmt.Errorf("删除现有关联失败: %w", err)
		}
		
		// 创建新关联
		if len(apiResourceIDs) > 0 {
			relations := make([]*models.ResourceAPIRelationModel, len(apiResourceIDs))
			for i, apiResourceID := range apiResourceIDs {
				relations[i] = &models.ResourceAPIRelationModel{
					TenantID:      appInfo.TenantID,
					InternalAppID: appInfo.InternalAppId,
					ResourceID:    resourceID,
					APIResourceID: apiResourceID,
				}
			}
			
			if err := tx.Create(relations).Error; err != nil {
				return fmt.Errorf("创建新关联失败: %w", err)
			}
		}
		
		return nil
	})
}

// GetResourcesWithAPIs 获取页面资源及其关联的API
func (r *ResourceAPIRelationRepositoryImpl) GetResourcesWithAPIs(ctx context.Context, params *repository.ResourceAPIQueryParams) ([]*repository.ResourceWithAPIs, error) {
	params.SetDefaults()
	
	// 构建基础查询
	query := r.GetDB().WithContext(ctx).Table("resource r").
		Select("r.*").
		Where("r.tenant_id = ?", params.TenantID)
	
	if params.InternalAppID > 0 {
		query = query.Where("r.internal_app_id = ?", params.InternalAppID)
	}
	
	if params.ResourceID != nil {
		query = query.Where("r.id = ?", *params.ResourceID)
	}
	
	if params.ResourceType != "" {
		query = query.Where("r.resource_type = ?", params.ResourceType)
	}
	
	// 分页
	query = query.Offset(params.GetOffset()).Limit(params.PageSize)
	
	var resourceModels []models.ResourceModel
	if err := query.Find(&resourceModels).Error; err != nil {
		return nil, fmt.Errorf("获取页面资源失败: %w", err)
	}
	
	// 为每个页面资源获取关联的API
	result := make([]*repository.ResourceWithAPIs, len(resourceModels))
	for i, resourceModel := range resourceModels {
		resource := resourceModel.ToDomainEntity()
		result[i] = &repository.ResourceWithAPIs{
			Resource: resource,
		}
		
		// 获取关联关系
		relations, err := r.GetByResourceID(ctx, resource.ID)
		if err != nil {
			return nil, fmt.Errorf("获取页面关联关系失败: %w", err)
		}
		result[i].Relations = relations
		
		// 获取API资源
		if len(relations) > 0 {
			apiResourceIDs := make([]int64, len(relations))
			for j, relation := range relations {
				apiResourceIDs[j] = relation.APIResourceID
			}
			
			var apiResourceModels []models.APIResourceModel
			if err := r.GetDB().WithContext(ctx).Where("id IN ?", apiResourceIDs).
				Find(&apiResourceModels).Error; err != nil {
				return nil, fmt.Errorf("获取API资源失败: %w", err)
			}
			
			apiResources := make([]*entity.APIResource, len(apiResourceModels))
			for j, model := range apiResourceModels {
				apiResources[j] = model.ToDomainEntity()
			}
			result[i].APIResources = apiResources
		}
	}
	
	return result, nil
}

// GetAPIsWithResources 获取API资源及其关联的页面资源
func (r *ResourceAPIRelationRepositoryImpl) GetAPIsWithResources(ctx context.Context, params *repository.APIResourceWithResourcesQueryParams) ([]*repository.APIResourceWithResources, error) {
	params.SetDefaults()
	
	// 构建基础查询
	query := r.GetDB().WithContext(ctx).Table("api_resource a").
		Select("a.*").
		Where("a.tenant_id = ?", params.TenantID)
	
	if params.InternalAppID > 0 {
		query = query.Where("a.internal_app_id = ?", params.InternalAppID)
	}
	
	if params.APIResourceID != nil {
		query = query.Where("a.id = ?", *params.APIResourceID)
	}
	
	if params.ServiceName != "" {
		query = query.Where("a.service_name = ?", params.ServiceName)
	}
	
	if params.APIMethod != "" {
		query = query.Where("a.api_method = ?", params.APIMethod)
	}
	
	// 分页
	query = query.Offset(params.GetOffset()).Limit(params.PageSize)
	
	var apiResourceModels []models.APIResourceModel
	if err := query.Find(&apiResourceModels).Error; err != nil {
		return nil, fmt.Errorf("获取API资源失败: %w", err)
	}
	
	// 为每个API资源获取关联的页面
	result := make([]*repository.APIResourceWithResources, len(apiResourceModels))
	for i, apiResourceModel := range apiResourceModels {
		apiResource := apiResourceModel.ToDomainEntity()
		result[i] = &repository.APIResourceWithResources{
			APIResource: apiResource,
		}
		
		// 获取关联关系
		relations, err := r.GetByAPIResourceID(ctx, apiResource.ID)
		if err != nil {
			return nil, fmt.Errorf("获取API关联关系失败: %w", err)
		}
		result[i].Relations = relations
		
		// 获取页面资源
		if len(relations) > 0 {
			resourceIDs := make([]int64, len(relations))
			for j, relation := range relations {
				resourceIDs[j] = relation.ResourceID
			}
			
			var resourceModels []models.ResourceModel
			if err := r.GetDB().WithContext(ctx).Where("id IN ?", resourceIDs).
				Find(&resourceModels).Error; err != nil {
				return nil, fmt.Errorf("获取页面资源失败: %w", err)
			}
			
			resources := make([]*entity.Resource, len(resourceModels))
			for j, model := range resourceModels {
				resources[j] = model.ToDomainEntity()
			}
			result[i].Resources = resources
		}
	}
	
	return result, nil
}