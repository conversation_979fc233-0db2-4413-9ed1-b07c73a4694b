package persistence

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/models"
	"gorm.io/gorm"
)

// APIResourceRepositoryImpl API资源仓储实现
type APIResourceRepositoryImpl struct {
	*BaseRepository[models.APIResourceModel]
}

// NewAPIResourceRepository 创建API资源仓储实例
func NewAPIResourceRepository(db *gorm.DB, logger logiface.Logger) repository.APIResourceRepository {
	return &APIResourceRepositoryImpl{
		BaseRepository: NewBaseRepository[models.APIResourceModel](db, logger),
	}
}

// Create 创建API资源
func (r *APIResourceRepositoryImpl) Create(ctx context.Context, apiResource *entity.APIResource) error {
	model := models.NewAPIResourceModelFromDomain(apiResource)
	
	if err := r.GetDB().WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("创建API资源失败: %w", err)
	}
	
	// 更新实体ID
	apiResource.ID = model.ID
	return nil
}

// Update 更新API资源
func (r *APIResourceRepositoryImpl) Update(ctx context.Context, apiResource *entity.APIResource) error {
	model := models.NewAPIResourceModelFromDomain(apiResource)
	
	result := r.GetDB().WithContext(ctx).Where("id = ? AND tenant_id = ?", 
		apiResource.ID, apiResource.TenantID).Updates(model)
	
	if result.Error != nil {
		return fmt.Errorf("更新API资源失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("API资源不存在或无权限访问")
	}
	
	return nil
}

// Delete 删除API资源
func (r *APIResourceRepositoryImpl) Delete(ctx context.Context, id int64, tenantID int64) error {
	result := r.GetDB().WithContext(ctx).Where("id = ? AND tenant_id = ?", id, tenantID).
		Delete(&models.APIResourceModel{})
	
	if result.Error != nil {
		return fmt.Errorf("删除API资源失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("API资源不存在或无权限访问")
	}
	
	return nil
}

// GetByID 根据ID获取API资源 (使用上下文中的租户过滤)
func (r *APIResourceRepositoryImpl) GetByID(ctx context.Context, id int64) (*entity.APIResource, error) {
	// 获取用户上下文进行租户过滤
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}
	
	var model models.APIResourceModel
	
	if err := r.GetDB().WithContext(ctx).Where("id = ? AND tenant_id = ?", id, appInfo.TenantID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("API资源不存在")
		}
		return nil, fmt.Errorf("获取API资源失败: %w", err)
	}
	
	return model.ToDomainEntity(), nil
}

// GetByIDWithTenant 根据ID和租户ID获取API资源
func (r *APIResourceRepositoryImpl) GetByIDWithTenant(ctx context.Context, id int64, tenantID int64) (*entity.APIResource, error) {
	var model models.APIResourceModel
	
	if err := r.GetDB().WithContext(ctx).Where("id = ? AND tenant_id = ?", id, tenantID).
		First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("API资源不存在")
		}
		return nil, fmt.Errorf("获取API资源失败: %w", err)
	}
	
	return model.ToDomainEntity(), nil
}

// List 列表查询API资源
func (r *APIResourceRepositoryImpl) List(ctx context.Context, params *repository.APIResourceQueryParams) ([]*entity.APIResource, error) {
	params.SetDefaults()
	
	query := r.GetDB().WithContext(ctx).Model(&models.APIResourceModel{})
	
	// 构建查询条件
	r.buildAPIResourceQuery(query, params)
	
	// 分页和排序
	query = query.Offset(params.GetOffset()).Limit(params.PageSize)
	query = query.Order(fmt.Sprintf("%s %s", params.SortBy, params.SortOrder))
	
	var modelList []models.APIResourceModel
	if err := query.Find(&modelList).Error; err != nil {
		return nil, fmt.Errorf("查询API资源列表失败: %w", err)
	}
	
	// 转换为领域实体
	entities := make([]*entity.APIResource, len(modelList))
	for i, model := range modelList {
		entities[i] = model.ToDomainEntity()
	}
	
	return entities, nil
}

// Count 统计API资源数量
func (r *APIResourceRepositoryImpl) Count(ctx context.Context, params *repository.APIResourceQueryParams) (int64, error) {
	query := r.GetDB().WithContext(ctx).Model(&models.APIResourceModel{})
	
	// 构建查询条件
	r.buildAPIResourceQuery(query, params)
	
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("统计API资源数量失败: %w", err)
	}
	
	return count, nil
}

// GetByPath 根据路径获取API资源
func (r *APIResourceRepositoryImpl) GetByPath(ctx context.Context, serviceName, method, path string, tenantID int64) (*entity.APIResource, error) {
	var model models.APIResourceModel
	
	if err := r.GetDB().WithContext(ctx).Where("service_name = ? AND api_method = ? AND path = ? AND tenant_id = ?", 
		serviceName, method, path, tenantID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("API资源不存在")
		}
		return nil, fmt.Errorf("获取API资源失败: %w", err)
	}
	
	return model.ToDomainEntity(), nil
}

// GetByServiceAndMethod 根据服务名和方法获取API资源
func (r *APIResourceRepositoryImpl) GetByServiceAndMethod(ctx context.Context, serviceName, method string, tenantID int64) ([]*entity.APIResource, error) {
	var modelList []models.APIResourceModel
	
	query := r.GetDB().WithContext(ctx).Where("service_name = ? AND tenant_id = ?", serviceName, tenantID)
	if method != "" {
		query = query.Where("api_method = ?", method)
	}
	
	if err := query.Find(&modelList).Error; err != nil {
		return nil, fmt.Errorf("获取API资源列表失败: %w", err)
	}
	
	entities := make([]*entity.APIResource, len(modelList))
	for i, model := range modelList {
		entities[i] = model.ToDomainEntity()
	}
	
	return entities, nil
}

// GetByPermissionID 根据权限ID获取API资源
func (r *APIResourceRepositoryImpl) GetByPermissionID(ctx context.Context, permissionID int64, tenantID int64) ([]*entity.APIResource, error) {
	var modelList []models.APIResourceModel
	
	if err := r.GetDB().WithContext(ctx).Where("permission_id = ? AND tenant_id = ?", 
		permissionID, tenantID).Find(&modelList).Error; err != nil {
		return nil, fmt.Errorf("获取API资源列表失败: %w", err)
	}
	
	entities := make([]*entity.APIResource, len(modelList))
	for i, model := range modelList {
		entities[i] = model.ToDomainEntity()
	}
	
	return entities, nil
}

// AssignPermission 分配权限给API资源
func (r *APIResourceRepositoryImpl) AssignPermission(ctx context.Context, apiResourceID, permissionID int64, tenantID int64) error {
	result := r.GetDB().WithContext(ctx).Model(&models.APIResourceModel{}).
		Where("id = ? AND tenant_id = ?", apiResourceID, tenantID).
		Update("permission_id", permissionID)
	
	if result.Error != nil {
		return fmt.Errorf("分配权限失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("API资源不存在或无权限访问")
	}
	
	return nil
}

// RemovePermission 移除API资源的权限
func (r *APIResourceRepositoryImpl) RemovePermission(ctx context.Context, apiResourceID int64, tenantID int64) error {
	result := r.GetDB().WithContext(ctx).Model(&models.APIResourceModel{}).
		Where("id = ? AND tenant_id = ?", apiResourceID, tenantID).
		Update("permission_id", nil)
	
	if result.Error != nil {
		return fmt.Errorf("移除权限失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("API资源不存在或无权限访问")
	}
	
	return nil
}

// BatchCreate 批量创建API资源
func (r *APIResourceRepositoryImpl) BatchCreate(ctx context.Context, apiResources []*entity.APIResource) error {
	if len(apiResources) == 0 {
		return nil
	}
	
	modelList := make([]*models.APIResourceModel, len(apiResources))
	for i, apiResource := range apiResources {
		modelList[i] = models.NewAPIResourceModelFromDomain(apiResource)
	}
	
	if err := r.GetDB().WithContext(ctx).Create(modelList).Error; err != nil {
		return fmt.Errorf("批量创建API资源失败: %w", err)
	}
	
	// 更新实体ID
	for i, model := range modelList {
		apiResources[i].ID = model.ID
	}
	
	return nil
}

// BatchUpdate 批量更新API资源
func (r *APIResourceRepositoryImpl) BatchUpdate(ctx context.Context, apiResources []*entity.APIResource) error {
	if len(apiResources) == 0 {
		return nil
	}
	
	return r.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, apiResource := range apiResources {
			model := models.NewAPIResourceModelFromDomain(apiResource)
			if err := tx.Where("id = ? AND tenant_id = ?", 
				apiResource.ID, apiResource.TenantID).Updates(model).Error; err != nil {
				return fmt.Errorf("批量更新API资源失败: %w", err)
			}
		}
		return nil
	})
}

// BatchDelete 批量删除API资源
func (r *APIResourceRepositoryImpl) BatchDelete(ctx context.Context, ids []int64, tenantID int64) error {
	if len(ids) == 0 {
		return nil
	}
	
	result := r.GetDB().WithContext(ctx).Where("id IN ? AND tenant_id = ?", ids, tenantID).
		Delete(&models.APIResourceModel{})
	
	if result.Error != nil {
		return fmt.Errorf("批量删除API资源失败: %w", result.Error)
	}
	
	return nil
}

// UpdateStatus 更新API资源状态
func (r *APIResourceRepositoryImpl) UpdateStatus(ctx context.Context, id int64, status string, tenantID int64) error {
	result := r.GetDB().WithContext(ctx).Model(&models.APIResourceModel{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Update("status", status)
	
	if result.Error != nil {
		return fmt.Errorf("更新API资源状态失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("API资源不存在或无权限访问")
	}
	
	return nil
}

// GetByStatus 根据状态获取API资源
func (r *APIResourceRepositoryImpl) GetByStatus(ctx context.Context, status string, tenantID int64) ([]*entity.APIResource, error) {
	var modelList []models.APIResourceModel
	
	if err := r.GetDB().WithContext(ctx).Where("status = ? AND tenant_id = ?", 
		status, tenantID).Find(&modelList).Error; err != nil {
		return nil, fmt.Errorf("获取API资源列表失败: %w", err)
	}
	
	entities := make([]*entity.APIResource, len(modelList))
	for i, model := range modelList {
		entities[i] = model.ToDomainEntity()
	}
	
	return entities, nil
}

// buildAPIResourceQuery 构建API资源查询条件
func (r *APIResourceRepositoryImpl) buildAPIResourceQuery(query *gorm.DB, params *repository.APIResourceQueryParams) {
	if params.TenantID > 0 {
		query = query.Where("tenant_id = ?", params.TenantID)
	}
	
	if params.InternalAppID > 0 {
		query = query.Where("internal_app_id = ?", params.InternalAppID)
	}
	
	if params.ServiceName != "" {
		query = query.Where("service_name = ?", params.ServiceName)
	}
	
	if params.APIMethod != "" {
		query = query.Where("api_method = ?", params.APIMethod)
	}
	
	if params.Path != "" {
		query = query.Where("path LIKE ?", "%"+params.Path+"%")
	}
	
	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}
	
	if params.IsPublic != nil {
		query = query.Where("is_public = ?", *params.IsPublic)
	}
	
	if params.PermissionID != nil {
		if *params.PermissionID == 0 {
			query = query.Where("permission_id IS NULL")
		} else {
			query = query.Where("permission_id = ?", *params.PermissionID)
		}
	}
	
	if params.Keyword != "" {
		keyword := "%" + params.Keyword + "%"
		query = query.Where("name LIKE ? OR display_name LIKE ? OR description LIKE ?", 
			keyword, keyword, keyword)
	}
}