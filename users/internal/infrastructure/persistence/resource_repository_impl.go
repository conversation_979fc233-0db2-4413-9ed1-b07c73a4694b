package persistence

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/models"

	"gorm.io/gorm"
)

// ResourceRepositoryImpl 资源仓储实现
type ResourceRepositoryImpl struct {
	*BaseRepository[models.ResourceModel]
}

// NewResourceRepository 创建资源仓储
func NewResourceRepository(db *gorm.DB, logger logiface.Logger) repository.ResourceRepository {
	return &ResourceRepositoryImpl{
		BaseRepository: NewBaseRepository[models.ResourceModel](db, logger),
	}
}

// Create 创建资源
func (r *ResourceRepositoryImpl) Create(ctx context.Context, resource *entity.Resource) error {
	// 启用参数模式，确保使用实体中设置的值而不是 context 中的值
	resourceModel := models.NewResourceModelFromDomainWithParameterMode(resource, true)
	return r.BaseRepository.Create(ctx, resourceModel)
}

// Update 更新资源
func (r *ResourceRepositoryImpl) Update(ctx context.Context, resource *entity.Resource) error {
	resourceModel := models.NewResourceModelFromDomain(resource)
	return r.BaseRepository.Update(ctx, resourceModel)
}

// Delete 删除资源
func (r *ResourceRepositoryImpl) Delete(ctx context.Context, id int64) error {
	return r.BaseRepository.Delete(ctx, id)
}

// FindByID 根据ID查找资源
func (r *ResourceRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.Resource, error) {
	resourceModel, err := r.BaseRepository.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if resourceModel == nil {
		return nil, nil
	}
	return resourceModel.ToDomainEntity(), nil
}

// FindByIDWithoutTenantInjection 根据ID查找资源，不注入tenantId（用于特殊场景）
func (r *ResourceRepositoryImpl) FindByIDWithoutTenantInjection(ctx context.Context, id int64) (*entity.Resource, error) {
	var resourceModel models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("id = ?", id).
		First(&resourceModel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return resourceModel.ToDomainEntity(), nil
}

// ExistsByName 检查名称是否存在
func (r *ResourceRepositoryImpl) ExistsByName(ctx context.Context, name string) (bool, error) {
	var count int64
	err := r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Where("name = ?", name).
		Count(&count).Error
	return count > 0, err
}

// List 获取资源列表
func (r *ResourceRepositoryImpl) List(ctx context.Context, offset, limit int) ([]entity.Resource, int64, error) {
	// 从 context 获取租户ID和应用ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	internalAppID := model.GetInternalAppIDFromContext(ctx)

	var resourceModels []models.ResourceModel
	var total int64

	// 获取总数
	err := r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取资源列表
	err = r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID).
		Order("sort_order ASC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&resourceModels).Error
	if err != nil {
		return nil, 0, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}

	return resources, total, nil
}

// Count 统计资源数量
func (r *ResourceRepositoryImpl) Count(ctx context.Context, params *repository.QueryParams) (int64, error) {
	// 从 context 获取租户ID和应用ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	internalAppID := model.GetInternalAppIDFromContext(ctx)

	var count int64
	query := r.GetDB().WithContext(ctx).Model(&models.ResourceModel{}).
		Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID)

	if params != nil {
		// 添加查询条件
		if params.Keyword != nil && *params.Keyword != "" {
			query = query.Where("name LIKE ? OR display_name LIKE ?", "%"+*params.Keyword+"%", "%"+*params.Keyword+"%")
		}
		if params.ResourceType != nil && *params.ResourceType != "" {
			query = query.Where("resource_type = ?", *params.ResourceType)
		}
	}

	err := query.Count(&count).Error
	return count, err
}

// FindAPIResourceByPathAndMethod 根据路径和方法查找API资源
func (r *ResourceRepositoryImpl) FindAPIResourceByPathAndMethod(ctx context.Context, path, method string, tenantID, internalAppID int64) (*entity.Resource, error) {
	var resourceModel models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND resource_type = ? AND path = ? AND api_method = ?",
			tenantID, internalAppID, "api", path, method).
		First(&resourceModel).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return resourceModel.ToDomainEntity(), nil
}

// FindAPIResources 查找API资源
func (r *ResourceRepositoryImpl) FindAPIResources(ctx context.Context, tenantID, internalAppID int64) ([]*entity.Resource, error) {
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND resource_type = ?", tenantID, internalAppID, "api").
		Find(&resourceModels).Error

	if err != nil {
		return nil, err
	}

	resources := make([]*entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = model.ToDomainEntity()
	}

	return resources, nil
}

// GetAPIResourcesByApp 根据应用获取API资源
func (r *ResourceRepositoryImpl) GetAPIResourcesByApp(ctx context.Context, internalAppID, tenantID int64) ([]*entity.Resource, error) {
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND resource_type = ?", tenantID, internalAppID, "api").
		Find(&resourceModels).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get API resources by app",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("internal_app_id", internalAppID),
		)
		return nil, err
	}

	resources := make([]*entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = model.ToDomainEntity()
	}

	return resources, nil
}

// Find 通用查询方法
func (r *ResourceRepositoryImpl) Find(ctx context.Context, params *repository.QueryParams) (*repository.QueryResult[*entity.Resource], error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[models.ResourceModel](r.db, ctx, models.ResourceModel{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 添加排序
	if params.OrderBy != "" {
		queryBuilder = queryBuilder.WithOrder(params.OrderBy, params.OrderDir)
	}

	// 添加分页
	if params.Limit > 0 {
		queryBuilder = queryBuilder.WithPagination(params.Offset/params.Limit+1, params.Limit)
	}

	// 执行查询
	resourceModels, err := queryBuilder.Find()
	if err != nil {
		return nil, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[models.ResourceModel](r.db, ctx, models.ResourceModel{}, r.logger).
		WithTenantFilter()
	countBuilder = r.buildQueryConditions(countBuilder, params)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}

	// 转换为指针切片
	resourcePtrs := make([]*entity.Resource, len(resources))
	for i := range resources {
		resourcePtrs[i] = &resources[i]
	}

	return repository.NewQueryResult(resourcePtrs, total, nil), nil
}

// buildQueryConditions 构建查询条件 - 使用 QueryBuilder 模式
func (r *ResourceRepositoryImpl) buildQueryConditions(qb *QueryBuilder[models.ResourceModel], params *repository.QueryParams) *QueryBuilder[models.ResourceModel] {
	// 基础条件 - 只处理通用字段
	if len(params.IDs) > 0 {
		qb = qb.WithInConditionInt64("id", params.IDs)
	}

	if params.TenantID != nil {
		qb = qb.WithCondition("tenant_id", "=", *params.TenantID)
	}

	if params.InternalAppID != nil {
		qb = qb.WithCondition("internal_app_id", "=", *params.InternalAppID)
	}

	if params.Status != nil {
		qb = qb.WithCondition("status", "=", *params.Status)
	}

	if params.IsSystem != nil {
		qb = qb.WithCondition("is_system", "=", *params.IsSystem)
	}

	// 时间范围条件 - 通用字段
	if params.CreatedAtStart != nil {
		qb = qb.WithCondition("created_at", ">=", *params.CreatedAtStart)
	}

	if params.CreatedAtEnd != nil {
		qb = qb.WithCondition("created_at", "<=", *params.CreatedAtEnd)
	}

	if params.UpdatedAtStart != nil {
		qb = qb.WithCondition("updated_at", ">=", *params.UpdatedAtStart)
	}

	if params.UpdatedAtEnd != nil {
		qb = qb.WithCondition("updated_at", "<=", *params.UpdatedAtEnd)
	}

	// 软删除处理
	if !params.WithDeleted {
		qb = qb.WithCondition("deleted_at", "IS", nil)
	}

	// 资源特定的查询条件
	if params.Keyword != nil && *params.Keyword != "" {
		qb = qb.WithLikeCondition("name", *params.Keyword)
		qb = qb.WithOrCondition("description", "LIKE", "%"+*params.Keyword+"%")
	}

	// 资源类型条件
	if params.ResourceType != nil {
		qb = qb.WithCondition("resource_type", "=", *params.ResourceType)
	}

	// 父级ID条件
	if params.ParentID != nil {
		qb = qb.WithCondition("parent_id", "=", *params.ParentID)
	}

	// 路径条件
	if params.Path != nil {
		qb = qb.WithCondition("path", "=", *params.Path)
	}

	// 公开级别条件
	if params.PublicLevel != nil {
		qb = qb.WithCondition("public_level", "=", *params.PublicLevel)
	}

	// 可分配条件
	if params.Assignable != nil {
		qb = qb.WithCondition("assignable", "=", *params.Assignable)
	}

	return qb
}

// GetByResourceType 根据资源类型获取资源
func (r *ResourceRepositoryImpl) GetByResourceType(ctx context.Context, resourceType value_object.ResourceType) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("resource_type = ? AND tenant_id = ? AND internal_app_id = ?", resourceType, tenantID, internalAppID).
		Order("sort_order ASC, id ASC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetByResourceTypes 根据资源类型列表获取资源
func (r *ResourceRepositoryImpl) GetByResourceTypes(ctx context.Context, resourceTypes []value_object.ResourceType) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("resource_type IN ? AND tenant_id = ? AND internal_app_id = ?", resourceTypes, tenantID, internalAppID).
		Order("sort_order ASC, id ASC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetByTenant 根据租户获取资源
func (r *ResourceRepositoryImpl) GetByTenant(ctx context.Context) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID).
		Order("sort_order ASC, created_at DESC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetChildren 获取子资源
func (r *ResourceRepositoryImpl) GetChildren(ctx context.Context, parentID int64) ([]entity.Resource, error) {
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("parent_id = ?", parentID).
		Order("sort_order ASC, id ASC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetRootResources 获取根资源（没有父资源的资源）
func (r *ResourceRepositoryImpl) GetRootResources(ctx context.Context) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND parent_id IS NULL", tenantID, internalAppID).
		Order("sort_order ASC, id ASC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetResourceTree 获取资源树
func (r *ResourceRepositoryImpl) GetResourceTree(ctx context.Context) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID).
		Order("sort_order ASC, id ASC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetByParent 根据父资源获取子资源
func (r *ResourceRepositoryImpl) GetByParent(ctx context.Context, parentID *int64) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	query := r.GetDB().WithContext(ctx).Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID)

	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else if *parentID == 0 {
		// 当 parentID 为 0 时，查询根节点（parent_id IS NULL）
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}

	err := query.Order("sort_order ASC, id ASC").Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetByPath 根据路径获取资源
func (r *ResourceRepositoryImpl) GetByPath(ctx context.Context, path string) (*entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModel models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("path = ? AND tenant_id = ? AND internal_app_id = ?", path, tenantID, internalAppID).
		First(&resourceModel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return resourceModel.ToDomainEntity(), nil
}

// ExistsByPath 检查路径是否存在
func (r *ResourceRepositoryImpl) ExistsByPath(ctx context.Context, path string) (bool, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var count int64
	err := r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Where("path = ? AND tenant_id = ? AND internal_app_id = ?", path, tenantID, internalAppID).
		Count(&count).Error
	return count > 0, err
}

// BatchCreate 批量创建资源
func (r *ResourceRepositoryImpl) BatchCreate(ctx context.Context, resources []entity.Resource) error {
	if len(resources) == 0 {
		return nil
	}

	// 将实体转换为模型
	resourceModels := make([]models.ResourceModel, len(resources))
	for i, resource := range resources {
		resourceModels[i] = *models.NewResourceModelFromDomain(&resource)
	}

	return r.GetDB().WithContext(ctx).Create(&resourceModels).Error
}

// BatchDelete 批量删除资源
func (r *ResourceRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	return r.GetDB().WithContext(ctx).Delete(&models.ResourceModel{}, ids).Error
}

// SoftDelete 软删除资源（如果需要）
func (r *ResourceRepositoryImpl) SoftDelete(ctx context.Context, id int64) error {
	return r.GetDB().WithContext(ctx).Delete(&models.ResourceModel{}, id).Error
}

// Restore 恢复软删除的资源（如果需要）
func (r *ResourceRepositoryImpl) Restore(ctx context.Context, id int64) error {
	return r.GetDB().WithContext(ctx).Unscoped().Model(&models.ResourceModel{}).Where("id = ?", id).Update("deleted_at", nil).Error
}

// UpdateSortOrder 更新排序
func (r *ResourceRepositoryImpl) UpdateSortOrder(ctx context.Context, id int64, sortOrder int) error {
	return r.GetDB().WithContext(ctx).Model(&models.ResourceModel{}).Where("id = ?", id).Update("sort_order", sortOrder).Error
}

// GetMaxSortOrder 获取最大排序值
func (r *ResourceRepositoryImpl) GetMaxSortOrder(ctx context.Context, parentID *int64) (int, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var maxSort int
	query := r.GetDB().WithContext(ctx).Model(&models.ResourceModel{}).Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID)

	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else if *parentID == 0 {
		// 当 parentID 为 0 时，查询根节点（parent_id IS NULL）
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}

	err := query.Select("COALESCE(MAX(sort_order), 0)").Scan(&maxSort).Error
	return maxSort, err
}

// GetResourcesWithPermissions 获取有权限的资源
func (r *ResourceRepositoryImpl) GetResourcesWithPermissions(ctx context.Context) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND id IN (SELECT DISTINCT target_resource_id FROM resource_relations WHERE tenant_id = ? AND internal_app_id = ?)", tenantID, internalAppID, tenantID, internalAppID).
		Order("sort_order ASC, id ASC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetResourcePermissions 获取资源的权限
func (r *ResourceRepositoryImpl) GetResourcePermissions(ctx context.Context, resourceID int64) ([]entity.Permission, error) {
	var permissions []entity.Permission
	err := r.GetDB().WithContext(ctx).
		Table("permissions").
		Joins("JOIN resource_relations rr ON permissions.id = rr.permission_id").
		Where("rr.target_resource_id = ? AND permissions.status = ?", resourceID, "active").
		Order("permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetPublicResources 获取公开资源
func (r *ResourceRepositoryImpl) GetPublicResources(ctx context.Context, publicLevel *value_object.PublicLevel, resourceType *value_object.ResourceType) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	query := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND is_public = ?", tenantID, internalAppID, true)

	if publicLevel != nil {
		query = query.Where("public_level = ?", *publicLevel)
	}

	if resourceType != nil {
		query = query.Where("resource_type = ?", *resourceType)
	}

	err := query.Order("sort_order ASC, created_at DESC").Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetPublicResourceByPath 根据路径获取公开资源
func (r *ResourceRepositoryImpl) GetPublicResourceByPath(ctx context.Context, path string) (*entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModel models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("path = ? AND tenant_id = ? AND internal_app_id = ? AND is_public = ?", path, tenantID, internalAppID, true).
		First(&resourceModel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return resourceModel.ToDomainEntity(), nil
}

// IsResourcePublic 检查资源是否公开
func (r *ResourceRepositoryImpl) IsResourcePublic(ctx context.Context, resourceID int64) (bool, error) {
	var resourceModel models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Select("is_public").
		Where("id = ?", resourceID).
		First(&resourceModel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}
	return resourceModel.IsPublic, nil
}

// UpdatePublicAccess 更新资源公开访问设置
func (r *ResourceRepositoryImpl) UpdatePublicAccess(ctx context.Context, resourceID int64, isPublic bool, publicLevel value_object.PublicLevel) error {
	return r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Where("id = ?", resourceID).
		Updates(map[string]interface{}{
			"is_public":    isPublic,
			"public_level": publicLevel,
		}).Error
}

// BatchUpdatePublicAccess 批量更新资源公开访问设置
func (r *ResourceRepositoryImpl) BatchUpdatePublicAccess(ctx context.Context, updates []struct {
	ResourceID  int64
	IsPublic    bool
	PublicLevel value_object.PublicLevel
}) error {
	return r.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			err := tx.Model(&models.ResourceModel{}).
				Where("id = ?", update.ResourceID).
				Updates(map[string]interface{}{
					"is_public":    update.IsPublic,
					"public_level": update.PublicLevel,
				}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// GetPublicResourcesCount 获取公开资源统计
func (r *ResourceRepositoryImpl) GetPublicResourcesCount(ctx context.Context) (map[string]int64, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	type CountResult struct {
		PublicLevel string `json:"public_level"`
		Count       int64  `json:"count"`
	}

	var results []CountResult
	err := r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Select("public_level, COUNT(*) as count").
		Where("tenant_id = ? AND is_public = ?", tenantID, true).
		Group("public_level").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	counts := make(map[string]int64)
	for _, result := range results {
		counts[result.PublicLevel] = result.Count
	}

	return counts, nil
}

// GetAnonymousResources 获取匿名可访问的资源
func (r *ResourceRepositoryImpl) GetAnonymousResources(ctx context.Context) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND is_public = ? AND public_level = ?", tenantID, internalAppID, true, value_object.PublicLevelAnonymous).
		Order("sort_order ASC, created_at DESC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetAuthenticatedResources 获取登录用户可访问的公开资源
func (r *ResourceRepositoryImpl) GetAuthenticatedResources(ctx context.Context) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND is_public = ? AND public_level IN ?",
			tenantID, internalAppID, true, []string{string(value_object.PublicLevelAnonymous), string(value_object.PublicLevelAuthenticated)}).
		Order("sort_order ASC, created_at DESC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetAssignableResources 获取可分配的资源
func (r *ResourceRepositoryImpl) GetAssignableResources(ctx context.Context, resourceType *value_object.ResourceType, includeSystem bool) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	query := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND assignable = ?", tenantID, internalAppID, true)

	if resourceType != nil {
		query = query.Where("resource_type = ?", *resourceType)
	}

	if !includeSystem {
		query = query.Where("is_system = ?", false)
	}

	err := query.Order("sort_order ASC, created_at DESC").Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetNonAssignableResources 获取不可分配的资源
func (r *ResourceRepositoryImpl) GetNonAssignableResources(ctx context.Context) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND assignable = ?", tenantID, internalAppID, false).
		Order("sort_order ASC, created_at DESC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// UpdateAssignability 更新资源可分配性
func (r *ResourceRepositoryImpl) UpdateAssignability(ctx context.Context, resourceID int64, assignable bool) error {
	return r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Where("id = ?", resourceID).
		Update("assignable", assignable).Error
}

// BatchUpdateAssignability 批量更新资源可分配性
func (r *ResourceRepositoryImpl) BatchUpdateAssignability(ctx context.Context, updates []struct {
	ResourceID int64
	Assignable bool
}) error {
	return r.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			err := tx.Model(&models.ResourceModel{}).
				Where("id = ?", update.ResourceID).
				Update("assignable", update.Assignable).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// GetAssignabilityStats 获取可分配性统计
func (r *ResourceRepositoryImpl) GetAssignabilityStats(ctx context.Context) (map[string]int64, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	type StatsResult struct {
		Assignable bool  `json:"assignable"`
		Count      int64 `json:"count"`
	}

	var results []StatsResult
	err := r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Select("assignable, COUNT(*) as count").
		Where("tenant_id = ? AND internal_app_id = ?", tenantID, internalAppID).
		Group("assignable").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int64)
	for _, result := range results {
		if result.Assignable {
			stats["assignable"] = result.Count
		} else {
			stats["non_assignable"] = result.Count
		}
	}

	// 获取按类型分组的统计
	type TypeStatsResult struct {
		ResourceType string `json:"resource_type"`
		Assignable   bool   `json:"assignable"`
		Count        int64  `json:"count"`
	}

	var typeResults []TypeStatsResult
	err = r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Select("resource_type, assignable, COUNT(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("resource_type, assignable").
		Scan(&typeResults).Error

	if err != nil {
		return stats, nil // 返回基础统计，类型统计失败不影响主要功能
	}

	for _, result := range typeResults {
		key := result.ResourceType
		if result.Assignable {
			key += "_assignable"
		} else {
			key += "_non_assignable"
		}
		stats[key] = result.Count
	}

	return stats, nil
}

// IsResourceAssignable 检查资源是否可分配
func (r *ResourceRepositoryImpl) IsResourceAssignable(ctx context.Context, resourceID int64) (bool, error) {
	var resourceModel models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Select("assignable").
		Where("id = ?", resourceID).
		First(&resourceModel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}
	return resourceModel.Assignable, nil
}

// GetUserAssignableResources 获取用户可分配的资源
func (r *ResourceRepositoryImpl) GetUserAssignableResources(ctx context.Context, resourceType *value_object.ResourceType) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	var resourceModels []models.ResourceModel
	query := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND assignable = ? AND is_system = ?", tenantID, internalAppID, true, false)

	if resourceType != nil {
		query = query.Where("resource_type = ?", *resourceType)
	}

	err := query.Order("sort_order ASC, created_at DESC").Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// 批量和分层查询优化方法

// GetResourcesByParentIDs 根据多个父ID批量获取子资源
func (r *ResourceRepositoryImpl) GetResourcesByParentIDs(ctx context.Context, parentIDs []int64) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	// 从 context 获取 internal_app_id
	internalAppID := model.GetInternalAppIDFromContext(ctx)
	if len(parentIDs) == 0 {
		return []entity.Resource{}, nil
	}

	var resourceModels []models.ResourceModel
	err := r.GetDB().WithContext(ctx).
		Where("tenant_id = ? AND internal_app_id = ? AND parent_id IN ?", tenantID, internalAppID, parentIDs).
		Order("parent_id ASC, sort_order ASC, id ASC").
		Find(&resourceModels).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}
	return resources, nil
}

// GetResourceTreeByLevels 按层级获取资源树（限制最大深度为4层）
func (r *ResourceRepositoryImpl) GetResourceTreeByLevels(ctx context.Context, maxDepth int, resourceType *value_object.ResourceType, internalAppID, tenantId *int64) ([]entity.Resource, error) {
	// 优先使用传入的 tenantId 参数，如果没有则从 context 获取
	var tenantID int64
	if tenantId != nil && *tenantId > 0 {
		tenantID = *tenantId
	} else {
		// 从 context 获取租户ID作为兜底
		if contextTenantID, exists := usercontext.GetTenantID(ctx); exists && contextTenantID > 0 {
			tenantID = contextTenantID
		} else {
			return nil, fmt.Errorf("tenant ID not found in parameters or context")
		}
	}

	// 使用显式传递的 internal_app_id 参数，不从context获取
	var appID int64
	if internalAppID != nil {
		appID = *internalAppID
	}

	// 限制最大深度为2层
	if maxDepth > 4 {
		maxDepth = 4
	}

	var allResources []entity.Resource

	// 第一次查询：获取根节点（第1层）
	var rootResources []models.ResourceModel
	rootQuery := r.GetDB().WithContext(ctx).Where("tenant_id = ? AND parent_id = 0", tenantID)

	// 如果指定了 internal_app_id，添加过滤条件
	if appID > 0 {
		rootQuery = rootQuery.Where("internal_app_id = ?", appID)
	}

	if resourceType != nil {
		rootQuery = rootQuery.Where("resource_type = ?", *resourceType)
	}

	err := rootQuery.Order("sort_order ASC, id ASC").Find(&rootResources).Error
	if err != nil {
		return nil, err
	}

	// 如果没有根节点，直接返回空结果
	if len(rootResources) == 0 {
		return []entity.Resource{}, nil
	}

	// 将模型转换为实体并添加到结果中
	for _, model := range rootResources {
		allResources = append(allResources, *model.ToDomainEntity())
	}

	// 如果只需要第1层，直接返回
	if maxDepth == 1 {
		return allResources, nil
	}

	// 第二次查询：获取子节点（第2层）
	// 收集所有根节点的ID
	var rootIDs []int64
	for _, root := range rootResources {
		rootIDs = append(rootIDs, root.ID)
	}

	var childResources []models.ResourceModel
	childQuery := r.GetDB().WithContext(ctx).Where("tenant_id = ? AND parent_id IN ?", tenantID, rootIDs)

	// 如果指定了 internal_app_id，添加过滤条件
	if appID > 0 {
		childQuery = childQuery.Where("internal_app_id = ?", appID)
	}

	if resourceType != nil {
		childQuery = childQuery.Where("resource_type = ?", *resourceType)
	}

	err = childQuery.Order("parent_id ASC, sort_order ASC, id ASC").Find(&childResources).Error
	if err != nil {
		return nil, err
	}

	// 将模型转换为实体并添加到结果中
	for _, model := range childResources {
		allResources = append(allResources, *model.ToDomainEntity())
	}

	return allResources, nil
}

// GetChildrenCounts 批量获取多个父节点的子节点数量
func (r *ResourceRepositoryImpl) GetChildrenCounts(ctx context.Context, parentIDs []int64) (map[int64]int, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	if len(parentIDs) == 0 {
		return make(map[int64]int), nil
	}

	type CountResult struct {
		ParentID int64 `json:"parent_id"`
		Count    int   `json:"count"`
	}

	var results []CountResult
	err := r.GetDB().WithContext(ctx).
		Model(&models.ResourceModel{}).
		Select("parent_id, COUNT(*) as count").
		Where("tenant_id = ? AND parent_id IN ?", tenantID, parentIDs).
		Group("parent_id").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	counts := make(map[int64]int)
	for _, result := range results {
		counts[result.ParentID] = result.Count
	}

	// 确保所有请求的parentID都有结果（即使是0）
	for _, parentID := range parentIDs {
		if _, exists := counts[parentID]; !exists {
			counts[parentID] = 0
		}
	}

	return counts, nil
}

// GetResourcesByLevel 获取指定层级的资源
func (r *ResourceRepositoryImpl) GetResourcesByLevel(ctx context.Context, level int, parentIDs []int64) ([]entity.Resource, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var resourceModels []models.ResourceModel
	var err error

	if level == 1 {
		// 第一层：根节点
		err = r.GetDB().WithContext(ctx).
			Where("tenant_id = ? AND parent_id IS NULL", tenantID).
			Order("sort_order ASC, id ASC").
			Find(&resourceModels).Error
	} else if len(parentIDs) > 0 {
		// 其他层级：指定父节点的子节点
		err = r.GetDB().WithContext(ctx).
			Where("tenant_id = ? AND parent_id IN ?", tenantID, parentIDs).
			Order("parent_id ASC, sort_order ASC, id ASC").
			Find(&resourceModels).Error
	}

	if err != nil {
		return nil, err
	}

	// 将模型转换为实体
	resources := make([]entity.Resource, len(resourceModels))
	for i, model := range resourceModels {
		resources[i] = *model.ToDomainEntity()
	}

	return resources, nil
}

// CheckHasDeepChildren 检查指定节点是否有超过指定深度的子节点
func (r *ResourceRepositoryImpl) CheckHasDeepChildren(ctx context.Context, parentIDs []int64, maxDepth int) (map[int64]bool, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	if len(parentIDs) == 0 {
		return make(map[int64]bool), nil
	}

	// 使用递归CTE检查是否有超过maxDepth的子节点
	query := `
		WITH RECURSIVE deep_check AS (
			-- 基础案例：直接子节点（level 1）
			SELECT parent_id, id, 1 as level
			FROM resource 
			WHERE tenant_id = ? AND parent_id IN (` + r.buildPlaceholders(len(parentIDs)) + `)
			
			UNION ALL
			
			-- 递归案例：更深层的子节点
			SELECT dc.parent_id, r.id, dc.level + 1
			FROM resource r
			INNER JOIN deep_check dc ON r.parent_id = dc.id
			WHERE dc.level < ?
		)
		SELECT DISTINCT parent_id, MAX(level) as max_level
		FROM deep_check 
		GROUP BY parent_id
	`

	args := []interface{}{tenantID}
	for _, parentID := range parentIDs {
		args = append(args, parentID)
	}
	args = append(args, maxDepth+1) // 检查是否有超过maxDepth的层级

	type DeepResult struct {
		ParentID int64 `json:"parent_id"`
		MaxLevel int   `json:"max_level"`
	}

	var results []DeepResult
	err := r.GetDB().WithContext(ctx).Raw(query, args...).Scan(&results).Error
	if err != nil {
		return nil, err
	}

	hasDeepChildren := make(map[int64]bool)
	for _, result := range results {
		hasDeepChildren[result.ParentID] = result.MaxLevel > maxDepth
	}

	// 确保所有请求的parentID都有结果
	for _, parentID := range parentIDs {
		if _, exists := hasDeepChildren[parentID]; !exists {
			hasDeepChildren[parentID] = false
		}
	}

	return hasDeepChildren, nil
}

// buildPlaceholders 构建SQL占位符字符串
func (r *ResourceRepositoryImpl) buildPlaceholders(count int) string {
	if count == 0 {
		return ""
	}

	placeholders := make([]string, count)
	for i := 0; i < count; i++ {
		placeholders[i] = "?"
	}
	return strings.Join(placeholders, ",")
}
