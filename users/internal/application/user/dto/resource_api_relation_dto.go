package dto

import (
	"time"
)

// CreateResourceAPIRelationRequest 创建页面与API关联关系请求
type CreateResourceAPIRelationRequest struct {
	ResourceID    int64  `json:"resource_id" validate:"required,gt=0"`
	APIResourceID int64  `json:"api_resource_id" validate:"required,gt=0"`
	IsRequired    bool   `json:"is_required"`                      // 是否必需
	Description   string `json:"description" validate:"max=255"`   // 关联关系描述
}

// ResourceAPIRelationResponse 页面与API关联关系响应
type ResourceAPIRelationResponse struct {
	ID            int64     `json:"id"`
	TenantID      int64     `json:"tenant_id"`      // 租户ID
	InternalAppID int64     `json:"internal_app_id"` // 内部应用ID
	ResourceID    int64     `json:"resource_id"`
	APIResourceID int64     `json:"api_resource_id"`
	IsRequired    bool      `json:"is_required"`     // 是否必需
	Description   string    `json:"description"`     // 关联关系描述
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// BatchAssignAPIsToResourceRequest 批量分配API给页面资源请求
type BatchAssignAPIsToResourceRequest struct {
	ResourceID     int64   `json:"resource_id" validate:"required,gt=0"`
	APIResourceIDs []int64 `json:"api_resource_ids" validate:"required,min=1"`
}

// ResourceWithAPIsResponse 页面资源及其关联的API响应
type ResourceWithAPIsResponse struct {
	Resource     *ResourceResponse      `json:"resource"`
	APIResources []*APIResourceResponse `json:"api_resources"`
	Relations    []*ResourceAPIRelationResponse `json:"relations"`
}

// APIResourceWithResourcesResponse API资源及其关联的页面资源响应
type APIResourceWithResourcesResponse struct {
	APIResource *APIResourceResponse     `json:"api_resource"`
	Resources   []*ResourceResponse      `json:"resources"`
	Relations   []*ResourceAPIRelationResponse `json:"relations"`
}

// ListResourcesWithAPIsRequest 列表查询页面资源及其API请求
type ListResourcesWithAPIsRequest struct {
	InternalAppID int64  `json:"internal_app_id"`
	ResourceID    *int64 `json:"resource_id"`
	ResourceType  string `json:"resource_type"`
	Page          int    `json:"page" validate:"min=1"`
	PageSize      int    `json:"page_size" validate:"min=1,max=100"`
}

// ListAPIsWithResourcesRequest 列表查询API资源及其页面请求
type ListAPIsWithResourcesRequest struct {
	InternalAppID int64  `json:"internal_app_id"`
	APIResourceID *int64 `json:"api_resource_id"`
	ServiceName   string `json:"service_name"`
	APIMethod     string `json:"api_method"`
	Page          int    `json:"page" validate:"min=1"`
	PageSize      int    `json:"page_size" validate:"min=1,max=100"`
}

// ResourceWithAPIsListResponse 页面资源及其API列表响应
type ResourceWithAPIsListResponse struct {
	Items      []*ResourceWithAPIsResponse `json:"items"`
	Total      int64                       `json:"total"`
	Page       int                         `json:"page"`
	PageSize   int                         `json:"page_size"`
	TotalPages int                         `json:"total_pages"`
}

// APIResourceWithResourcesListResponse API资源及其页面列表响应
type APIResourceWithResourcesListResponse struct {
	Items      []*APIResourceWithResourcesResponse `json:"items"`
	Total      int64                               `json:"total"`
	Page       int                                 `json:"page"`
	PageSize   int                                 `json:"page_size"`
	TotalPages int                                 `json:"total_pages"`
}