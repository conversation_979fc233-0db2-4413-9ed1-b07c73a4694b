package dto

import (
	"time"
)

// CreateAPIResourceRequest 创建API资源请求
type CreateAPIResourceRequest struct {
	Name          string  `json:"name" validate:"required,max=100"`
	DisplayName   string  `json:"display_name" validate:"max=100"`
	Description   string  `json:"description" validate:"max=255"`
	PermissionID  *int64  `json:"permission_id"`
	ServiceName   string  `json:"service_name" validate:"required,max=100"`
	APIMethod     string  `json:"api_method" validate:"required,oneof=GET POST PUT DELETE PATCH"`
	Path          string  `json:"path" validate:"required,max=255"`
	RequestType   string  `json:"request_type" validate:"max=50"`
	ResponseType  string  `json:"response_type" validate:"max=50"`
	ContentType   string  `json:"content_type" validate:"max=100"`
	IsPublic      bool    `json:"is_public"`
	Status        string  `json:"status" validate:"oneof=active inactive"`
}

// UpdateAPIResourceRequest 更新API资源请求
type UpdateAPIResourceRequest struct {
	ID            int64   `json:"id" validate:"required,gt=0"`
	Name          string  `json:"name" validate:"required,max=100"`
	DisplayName   string  `json:"display_name" validate:"max=100"`
	Description   string  `json:"description" validate:"max=255"`
	PermissionID  *int64  `json:"permission_id"`
	ServiceName   string  `json:"service_name" validate:"required,max=100"`
	APIMethod     string  `json:"api_method" validate:"required,oneof=GET POST PUT DELETE PATCH"`
	Path          string  `json:"path" validate:"required,max=255"`
	RequestType   string  `json:"request_type" validate:"max=50"`
	ResponseType  string  `json:"response_type" validate:"max=50"`
	ContentType   string  `json:"content_type" validate:"max=100"`
	IsPublic      bool    `json:"is_public"`
	Status        string  `json:"status" validate:"oneof=active inactive"`
}

// ListAPIResourcesRequest 列表查询API资源请求
type ListAPIResourcesRequest struct {
	InternalAppID int64   `json:"internal_app_id"`
	ServiceName   string  `json:"service_name"`
	APIMethod     string  `json:"api_method"`
	Path          string  `json:"path"`
	Status        string  `json:"status"`
	IsPublic      *bool   `json:"is_public"`
	PermissionID  *int64  `json:"permission_id"`
	Keyword       string  `json:"keyword"`
	Page          int     `json:"page" validate:"min=1"`
	PageSize      int     `json:"page_size" validate:"min=1,max=100"`
	SortBy        string  `json:"sort_by"`
	SortOrder     string  `json:"sort_order" validate:"oneof=asc desc"`
}

// APIResourceResponse API资源响应
type APIResourceResponse struct {
	ID            int64     `json:"id"`
	TenantID      int64     `json:"tenant_id"`
	InternalAppID int64     `json:"internal_app_id"`
	Name          string    `json:"name"`
	DisplayName   string    `json:"display_name"`
	Description   string    `json:"description"`
	PermissionID  *int64    `json:"permission_id"`
	ServiceName   string    `json:"service_name"`
	APIMethod     string    `json:"api_method"`
	Path          string    `json:"path"`
	RequestType   string    `json:"request_type"`
	ResponseType  string    `json:"response_type"`
	ContentType   string    `json:"content_type"`
	IsPublic      bool      `json:"is_public"`
	Status        string    `json:"status"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// APIResourceListResponse API资源列表响应
type APIResourceListResponse struct {
	Items      []*APIResourceResponse `json:"items"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// AssignPermissionToAPIResourceRequest 分配权限给API资源请求
type AssignPermissionToAPIResourceRequest struct {
	APIResourceID int64 `json:"api_resource_id" validate:"required,gt=0"`
	PermissionID  int64 `json:"permission_id" validate:"required,gt=0"`
}

// BatchCreateAPIResourcesRequest 批量创建API资源请求
type BatchCreateAPIResourcesRequest struct {
	APIResources []*CreateAPIResourceRequest `json:"api_resources" validate:"required,min=1,max=100"`
}

// BatchDeleteAPIResourcesRequest 批量删除API资源请求
type BatchDeleteAPIResourcesRequest struct {
	IDs []int64 `json:"ids" validate:"required,min=1"`
}

// UpdateAPIResourceStatusRequest 更新API资源状态请求
type UpdateAPIResourceStatusRequest struct {
	ID     int64  `json:"id" validate:"required,gt=0"`
	Status string `json:"status" validate:"required,oneof=active inactive"`
}