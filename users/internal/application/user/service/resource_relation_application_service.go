package service

import (
	"context"
	"fmt"
	"math"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
)

// ResourceRelationApplicationService 资源关系应用服务
type ResourceRelationApplicationService struct {
	logger                      logiface.Logger
	resourceRelationRepository  repository.ResourceRelationRepository
	resourceRepository          repository.ResourceRepository
}

// NewResourceRelationApplicationService 创建资源关系应用服务
func NewResourceRelationApplicationService(
	logger logiface.Logger,
	resourceRelationRepository repository.ResourceRelationRepository,
	resourceRepository repository.ResourceRepository,
) *ResourceRelationApplicationService {
	return &ResourceRelationApplicationService{
		logger:                      logger,
		resourceRelationRepository: resourceRelationRepository,
		resourceRepository:         resourceRepository,
	}
}

// CreateResourceRelation 创建资源关系
func (s *ResourceRelationApplicationService) CreateResourceRelation(ctx context.Context, req *dto.CreateResourceRelationRequest) (*dto.ResourceRelationResponse, error) {
	// 1. 验证源资源和目标资源是否存在
	sourceResource, err := s.resourceRepository.FindByID(ctx, req.SourceResourceID)
	if err != nil {
		s.logger.Error(ctx, "find source resource failed", logiface.Error(err))
		return nil, errors.NewUserError(errors.CodeResourceNotFound, "源资源不存在")
	}
	if sourceResource == nil {
		return nil, errors.NewUserError(errors.CodeResourceNotFound, "源资源不存在")
	}

	targetResource, err := s.resourceRepository.FindByID(ctx, req.TargetResourceID)
	if err != nil {
		s.logger.Error(ctx, "find target resource failed", logiface.Error(err))
		return nil, errors.NewUserError(errors.CodeResourceNotFound, "目标资源不存在")
	}
	if targetResource == nil {
		return nil, errors.NewUserError(errors.CodeResourceNotFound, "目标资源不存在")
	}

	// 2. 检查关系是否已存在
	existingRelation, err := s.resourceRelationRepository.FindBySourceAndTarget(
		ctx, req.SourceResourceID, req.TargetResourceID, req.TenantID, req.InternalAppID)
	if err != nil {
		s.logger.Error(ctx, "check existing relation failed", logiface.Error(err))
		return nil, fmt.Errorf("检查关系失败: %v", err)
	}
	if existingRelation != nil {
		return nil, errors.NewUserError(errors.CodeResourceRelationExists, "资源关系已存在")
	}

	// 3. 创建资源关系实体
	relation := entity.NewResourceRelation(
		req.TenantID,
		req.InternalAppID,
		req.SourceResourceID,
		req.TargetResourceID,
		entity.WithPermissionConfig(req.PermissionCode, req.IsRequired, req.InheritParent, req.Priority),
		entity.WithDescriptionOption(req.Description),
		entity.WithStatusOption(req.Status),
	)

	// 4. 验证实体
	if err := relation.ValidatePermissionConfig(); err != nil {
		return nil, errors.NewUserError(errors.CodeValidationFailed, err.Error())
	}

	// 5. 保存到数据库
	if err := s.resourceRelationRepository.Create(ctx, relation); err != nil {
		s.logger.Error(ctx, "create resource relation failed", logiface.Error(err))
		return nil, fmt.Errorf("创建资源关系失败: %v", err)
	}

	// 6. 转换为响应DTO
	response := s.convertToResourceRelationResponse(relation)
	response.SourceResource = s.convertToResourceResponse(sourceResource)
	response.TargetResource = s.convertToResourceResponse(targetResource)

	s.logger.Info(ctx, "resource relation created successfully",
		logiface.Int64("relation_id", relation.ID),
		logiface.Int64("source_resource_id", req.SourceResourceID),
		logiface.Int64("target_resource_id", req.TargetResourceID))

	return response, nil
}

// UpdateResourceRelation 更新资源关系
func (s *ResourceRelationApplicationService) UpdateResourceRelation(ctx context.Context, req *dto.UpdateResourceRelationRequest) (*dto.ResourceRelationResponse, error) {
	// 1. 查找资源关系
	relation, err := s.resourceRelationRepository.FindByID(ctx, req.ID)
	if err != nil {
		s.logger.Error(ctx, "find resource relation failed", logiface.Error(err))
		return nil, fmt.Errorf("查找资源关系失败: %v", err)
	}
	if relation == nil {
		return nil, errors.NewUserError(errors.CodeResourceRelationNotFound, "资源关系不存在")
	}

	// 2. 更新字段
	if req.Description != nil {
		relation.Description = *req.Description
	}
	if req.PermissionCode != nil {
		relation.PermissionCode = *req.PermissionCode
	}
	if req.IsRequired != nil {
		relation.IsRequired = *req.IsRequired
	}
	if req.InheritParent != nil {
		relation.InheritParent = *req.InheritParent
	}
	if req.Priority != nil {
		relation.Priority = *req.Priority
	}
	if req.Status != nil {
		relation.Status = *req.Status
	}

	// 3. 验证更新后的实体
	if err := relation.ValidatePermissionConfig(); err != nil {
		return nil, errors.NewUserError(errors.CodeValidationFailed, err.Error())
	}

	// 4. 保存更新
	if err := s.resourceRelationRepository.Update(ctx, relation); err != nil {
		s.logger.Error(ctx, "update resource relation failed", logiface.Error(err))
		return nil, fmt.Errorf("更新资源关系失败: %v", err)
	}

	// 5. 转换为响应DTO
	response := s.convertToResourceRelationResponse(relation)

	s.logger.Info(ctx, "resource relation updated successfully",
		logiface.Int64("relation_id", relation.ID))

	return response, nil
}

// GetResourceRelations 获取资源关系列表
func (s *ResourceRelationApplicationService) GetResourceRelations(ctx context.Context, req *dto.GetResourceRelationsRequest) (*dto.PagedResourceRelationResponse, error) {
	// 1. 构建查询条件
	conditions := make(map[string]interface{})
	if req.TenantID != nil {
		conditions["tenant_id"] = *req.TenantID
	}
	if req.InternalAppID != nil {
		conditions["internal_app_id"] = *req.InternalAppID
	}
	if req.SourceResourceID != nil {
		conditions["source_resource_id"] = *req.SourceResourceID
	}
	if req.TargetResourceID != nil {
		conditions["target_resource_id"] = *req.TargetResourceID
	}
	if req.Status != nil {
		conditions["status"] = *req.Status
	}

	// 2. 查询总数
	total, err := s.resourceRelationRepository.Count(ctx, conditions)
	if err != nil {
		s.logger.Error(ctx, "count resource relations failed", logiface.Error(err))
		return nil, fmt.Errorf("查询总数失败: %v", err)
	}

	// 3. 查询列表
	offset := (req.Page - 1) * req.Size
	relations, err := s.resourceRelationRepository.FindWithConditions(ctx, conditions, offset, req.Size)
	if err != nil {
		s.logger.Error(ctx, "find resource relations failed", logiface.Error(err))
		return nil, fmt.Errorf("查询关系列表失败: %v", err)
	}

	// 4. 转换为响应DTO
	relationResponses := make([]*dto.ResourceRelationResponse, len(relations))
	for i, relation := range relations {
		relationResponses[i] = s.convertToResourceRelationResponse(relation)
	}

	// 5. 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(req.Size)))

	return &dto.PagedResourceRelationResponse{
		List:       relationResponses,
		Total:      total,
		Page:       req.Page,
		Size:       req.Size,
		TotalPages: totalPages,
	}, nil
}

// DeleteResourceRelation 删除资源关系
func (s *ResourceRelationApplicationService) DeleteResourceRelation(ctx context.Context, req *dto.DeleteResourceRelationRequest) error {
	// 1. 查找资源关系
	relation, err := s.resourceRelationRepository.FindByID(ctx, req.ID)
	if err != nil {
		s.logger.Error(ctx, "find resource relation failed", logiface.Error(err))
		return fmt.Errorf("查找资源关系失败: %v", err)
	}
	if relation == nil {
		return errors.NewUserError(errors.CodeResourceRelationNotFound, "资源关系不存在")
	}

	// 2. 删除关系
	if err := s.resourceRelationRepository.Delete(ctx, req.ID); err != nil {
		s.logger.Error(ctx, "delete resource relation failed", logiface.Error(err))
		return fmt.Errorf("删除资源关系失败: %v", err)
	}

	s.logger.Info(ctx, "resource relation deleted successfully",
		logiface.Int64("relation_id", req.ID))

	return nil
}

// ToggleResourceRelationStatus 切换资源关系状态
func (s *ResourceRelationApplicationService) ToggleResourceRelationStatus(ctx context.Context, req *dto.ToggleResourceRelationStatusRequest) (*dto.ResourceRelationResponse, error) {
	// 1. 查找资源关系
	relation, err := s.resourceRelationRepository.FindByID(ctx, req.ID)
	if err != nil {
		s.logger.Error(ctx, "find resource relation failed", logiface.Error(err))
		return nil, fmt.Errorf("查找资源关系失败: %v", err)
	}
	if relation == nil {
		return nil, errors.NewUserError(errors.CodeResourceRelationNotFound, "资源关系不存在")
	}

	// 2. 更新状态
	relation.Status = req.Status
	if req.Status == "active" {
		relation.Activate()
	} else {
		relation.Deactivate()
	}

	// 3. 保存更新
	if err := s.resourceRelationRepository.Update(ctx, relation); err != nil {
		s.logger.Error(ctx, "update resource relation status failed", logiface.Error(err))
		return nil, fmt.Errorf("更新关系状态失败: %v", err)
	}

	// 4. 转换为响应DTO
	response := s.convertToResourceRelationResponse(relation)

	s.logger.Info(ctx, "resource relation status toggled successfully",
		logiface.Int64("relation_id", relation.ID),
		logiface.String("new_status", req.Status))

	return response, nil
}

// convertToResourceRelationResponse 转换为资源关系响应DTO
func (s *ResourceRelationApplicationService) convertToResourceRelationResponse(relation *entity.ResourceRelation) *dto.ResourceRelationResponse {
	return &dto.ResourceRelationResponse{
		ID:               relation.ID,
		TenantID:         relation.TenantID,
		InternalAppID:    relation.InternalAppID,
		SourceResourceID: relation.SourceResourceID,
		TargetResourceID: relation.TargetResourceID,
		Description:      relation.Description,
		PermissionCode:   relation.PermissionCode,
		IsRequired:       relation.IsRequired,
		InheritParent:    relation.InheritParent,
		Priority:         relation.Priority,
		Status:           relation.Status,
		StatusDisplay:    relation.GetDisplayStatus(),
		CreatedAt:        relation.CreatedAt,
		UpdatedAt:        relation.UpdatedAt,
	}
}

// convertToResourceResponse 转换为资源响应DTO
func (s *ResourceRelationApplicationService) convertToResourceResponse(resource *entity.Resource) *dto.ResourceResponse {
	return &dto.ResourceResponse{
		ID:            resource.ID,
		TenantID:      resource.TenantID,
		InternalAppID: resource.InternalAppID,
		Name:          resource.Name,
		DisplayName:   resource.DisplayName,
		Description:   resource.Description,
		ResourceType:  resource.ResourceType.String(),
		PermissionID:  resource.PermissionID,
		ParentID:      resource.ParentID,
		Path:          resource.Path,
		Icon:          resource.Icon,
		SortOrder:     resource.SortOrder,
		IsSystem:      resource.IsSystem,
		IsPublic:      resource.IsPublic,
		PublicLevel:   resource.PublicLevel.String(),
		Assignable:    resource.Assignable,
		CreatedAt:     resource.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     resource.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}