package service

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// APIResourceApplicationService API资源应用服务
type APIResourceApplicationService struct {
	apiResourceRepo repository.APIResourceRepository
	permissionRepo  repository.PermissionRepository
	baseService     *BaseService
}

// NewAPIResourceApplicationService 创建API资源应用服务实例
func NewAPIResourceApplicationService(
	apiResourceRepo repository.APIResourceRepository,
	permissionRepo repository.PermissionRepository,
) *APIResourceApplicationService {
	return &APIResourceApplicationService{
		apiResourceRepo: apiResourceRepo,
		permissionRepo:  permissionRepo,
		baseService:     NewBaseService(),
	}
}

// CreateAPIResource 创建API资源
func (s *APIResourceApplicationService) CreateAPIResource(ctx context.Context, req *dto.CreateAPIResourceRequest) (*dto.APIResourceResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 验证权限ID是否存在
	if req.PermissionID != nil && *req.PermissionID > 0 {
		_, err := s.permissionRepo.FindByID(ctx, *req.PermissionID)
		if err != nil {
			return nil, fmt.Errorf("权限不存在: %w", err)
		}
	}

	// 检查API路径是否已存在
	existing, _ := s.apiResourceRepo.GetByPath(ctx, req.ServiceName, req.APIMethod, req.Path, appInfo.TenantID)
	if existing != nil {
		return nil, fmt.Errorf("API路径已存在: %s %s", req.APIMethod, req.Path)
	}

	// 创建API资源实体
	apiResource := &entity.APIResource{
		TenantID:      appInfo.TenantID,
		InternalAppID: appInfo.InternalAppId,
		Name:          req.Name,
		DisplayName:   req.DisplayName,
		Description:   req.Description,
		PermissionID:  req.PermissionID,
		ServiceName:   req.ServiceName,
		APIMethod:     req.APIMethod,
		Path:          req.Path,
		RequestType:   req.RequestType,
		ResponseType:  req.ResponseType,
		ContentType:   req.ContentType,
		IsPublic:      req.IsPublic,
		Status:        req.Status,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 设置默认值
	if apiResource.RequestType == "" {
		apiResource.RequestType = "json"
	}
	if apiResource.ResponseType == "" {
		apiResource.ResponseType = "json"
	}
	if apiResource.Status == "" {
		apiResource.Status = "active"
	}

	// 验证实体数据
	if err := apiResource.Validate(); err != nil {
		return nil, fmt.Errorf("API资源数据验证失败: %w", err)
	}

	// 保存到数据库
	if err := s.apiResourceRepo.Create(ctx, apiResource); err != nil {
		return nil, fmt.Errorf("创建API资源失败: %w", err)
	}

	return s.toAPIResourceResponse(apiResource), nil
}

// UpdateAPIResource 更新API资源
func (s *APIResourceApplicationService) UpdateAPIResource(ctx context.Context, req *dto.UpdateAPIResourceRequest) error {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}

	// 获取现有API资源
	existing, err := s.apiResourceRepo.GetByIDWithTenant(ctx, req.ID, appInfo.TenantID)
	if err != nil {
		return fmt.Errorf("API资源不存在: %w", err)
	}

	// 验证权限ID是否存在
	if req.PermissionID != nil && *req.PermissionID > 0 {
		_, err := s.permissionRepo.FindByID(ctx, *req.PermissionID)
		if err != nil {
			return fmt.Errorf("权限不存在: %w", err)
		}
	}

	// 检查API路径是否冲突（排除当前记录）
	if existing.ServiceName != req.ServiceName || existing.APIMethod != req.APIMethod || existing.Path != req.Path {
		conflicting, _ := s.apiResourceRepo.GetByPath(ctx, req.ServiceName, req.APIMethod, req.Path, appInfo.TenantID)
		if conflicting != nil && conflicting.ID != req.ID {
			return fmt.Errorf("API路径已存在: %s %s", req.APIMethod, req.Path)
		}
	}

	// 更新实体数据
	existing.Name = req.Name
	existing.DisplayName = req.DisplayName
	existing.Description = req.Description
	existing.PermissionID = req.PermissionID
	existing.ServiceName = req.ServiceName
	existing.APIMethod = req.APIMethod
	existing.Path = req.Path
	existing.RequestType = req.RequestType
	existing.ResponseType = req.ResponseType
	existing.ContentType = req.ContentType
	existing.IsPublic = req.IsPublic
	existing.Status = req.Status
	existing.UpdatedAt = time.Now()

	// 设置默认值
	if existing.RequestType == "" {
		existing.RequestType = "json"
	}
	if existing.ResponseType == "" {
		existing.ResponseType = "json"
	}

	// 验证实体数据
	if err := existing.Validate(); err != nil {
		return fmt.Errorf("API资源数据验证失败: %w", err)
	}

	// 更新到数据库
	if err := s.apiResourceRepo.Update(ctx, existing); err != nil {
		return fmt.Errorf("更新API资源失败: %w", err)
	}

	return nil
}

// DeleteAPIResource 删除API资源
func (s *APIResourceApplicationService) DeleteAPIResource(ctx context.Context, id int64) error {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}

	// 检查API资源是否存在
	_, err := s.apiResourceRepo.GetByIDWithTenant(ctx, id, appInfo.TenantID)
	if err != nil {
		return fmt.Errorf("API资源不存在: %w", err)
	}

	// 删除API资源
	if err := s.apiResourceRepo.Delete(ctx, id, appInfo.TenantID); err != nil {
		return fmt.Errorf("删除API资源失败: %w", err)
	}

	return nil
}

// GetAPIResource 获取API资源详情
func (s *APIResourceApplicationService) GetAPIResource(ctx context.Context, id int64) (*dto.APIResourceResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 获取API资源
	apiResource, err := s.apiResourceRepo.GetByIDWithTenant(ctx, id, appInfo.TenantID)
	if err != nil {
		return nil, fmt.Errorf("获取API资源失败: %w", err)
	}

	return s.toAPIResourceResponse(apiResource), nil
}

// ListAPIResources 列表查询API资源
func (s *APIResourceApplicationService) ListAPIResources(ctx context.Context, req *dto.ListAPIResourcesRequest) (*dto.APIResourceListResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 构建查询参数
	params := &repository.APIResourceQueryParams{
		TenantID:      appInfo.TenantID,
		InternalAppID: req.InternalAppID,
		ServiceName:   req.ServiceName,
		APIMethod:     req.APIMethod,
		Path:          req.Path,
		Status:        req.Status,
		IsPublic:      req.IsPublic,
		PermissionID:  req.PermissionID,
		Keyword:       req.Keyword,
		Page:          req.Page,
		PageSize:      req.PageSize,
		SortBy:        req.SortBy,
		SortOrder:     req.SortOrder,
	}

	if req.InternalAppID == 0 {
		params.InternalAppID = appInfo.InternalAppId
	}

	params.SetDefaults()

	// 查询API资源列表
	apiResources, err := s.apiResourceRepo.List(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("查询API资源列表失败: %w", err)
	}

	// 统计总数
	total, err := s.apiResourceRepo.Count(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("统计API资源数量失败: %w", err)
	}

	// 转换为响应格式
	items := make([]*dto.APIResourceResponse, len(apiResources))
	for i, apiResource := range apiResources {
		items[i] = s.toAPIResourceResponse(apiResource)
	}

	totalPages := int((total + int64(params.PageSize) - 1) / int64(params.PageSize))

	return &dto.APIResourceListResponse{
		Items:      items,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// AssignPermissionToAPIResource 分配权限给API资源
func (s *APIResourceApplicationService) AssignPermissionToAPIResource(ctx context.Context, req *dto.AssignPermissionToAPIResourceRequest) error {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}

	// 验证权限是否存在
	_, err := s.permissionRepo.FindByID(ctx, req.PermissionID)
	if err != nil {
		return fmt.Errorf("权限不存在: %w", err)
	}

	// 验证API资源是否存在
	_, err = s.apiResourceRepo.GetByIDWithTenant(ctx, req.APIResourceID, appInfo.TenantID)
	if err != nil {
		return fmt.Errorf("API资源不存在: %w", err)
	}

	// 分配权限
	if err := s.apiResourceRepo.AssignPermission(ctx, req.APIResourceID, req.PermissionID, appInfo.TenantID); err != nil {
		return fmt.Errorf("分配权限失败: %w", err)
	}

	return nil
}

// BatchCreateAPIResources 批量创建API资源
func (s *APIResourceApplicationService) BatchCreateAPIResources(ctx context.Context, req *dto.BatchCreateAPIResourcesRequest) ([]*dto.APIResourceResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 转换为实体
	apiResources := make([]*entity.APIResource, len(req.APIResources))
	for i, apiReq := range req.APIResources {
		// 验证权限ID是否存在
		if apiReq.PermissionID != nil && *apiReq.PermissionID > 0 {
			_, err := s.permissionRepo.FindByID(ctx, *apiReq.PermissionID)
			if err != nil {
				return nil, fmt.Errorf("第%d个API资源权限不存在: %w", i+1, err)
			}
		}

		apiResource := &entity.APIResource{
			TenantID:      appInfo.TenantID,
			InternalAppID: appInfo.InternalAppId,
			Name:          apiReq.Name,
			DisplayName:   apiReq.DisplayName,
			Description:   apiReq.Description,
			PermissionID:  apiReq.PermissionID,
			ServiceName:   apiReq.ServiceName,
			APIMethod:     apiReq.APIMethod,
			Path:          apiReq.Path,
			RequestType:   apiReq.RequestType,
			ResponseType:  apiReq.ResponseType,
			ContentType:   apiReq.ContentType,
			IsPublic:      apiReq.IsPublic,
			Status:        apiReq.Status,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		// 设置默认值
		if apiResource.RequestType == "" {
			apiResource.RequestType = "json"
		}
		if apiResource.ResponseType == "" {
			apiResource.ResponseType = "json"
		}
		if apiResource.Status == "" {
			apiResource.Status = "active"
		}

		// 验证实体数据
		if err := apiResource.Validate(); err != nil {
			return nil, fmt.Errorf("第%d个API资源数据验证失败: %w", i+1, err)
		}

		apiResources[i] = apiResource
	}

	// 批量创建
	if err := s.apiResourceRepo.BatchCreate(ctx, apiResources); err != nil {
		return nil, fmt.Errorf("批量创建API资源失败: %w", err)
	}

	// 转换为响应格式
	responses := make([]*dto.APIResourceResponse, len(apiResources))
	for i, apiResource := range apiResources {
		responses[i] = s.toAPIResourceResponse(apiResource)
	}

	return responses, nil
}

// BatchDeleteAPIResources 批量删除API资源
func (s *APIResourceApplicationService) BatchDeleteAPIResources(ctx context.Context, req *dto.BatchDeleteAPIResourcesRequest) error {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}

	// 批量删除
	if err := s.apiResourceRepo.BatchDelete(ctx, req.IDs, appInfo.TenantID); err != nil {
		return fmt.Errorf("批量删除API资源失败: %w", err)
	}

	return nil
}

// UpdateAPIResourceStatus 更新API资源状态
func (s *APIResourceApplicationService) UpdateAPIResourceStatus(ctx context.Context, req *dto.UpdateAPIResourceStatusRequest) error {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}

	// 更新状态
	if err := s.apiResourceRepo.UpdateStatus(ctx, req.ID, req.Status, appInfo.TenantID); err != nil {
		return fmt.Errorf("更新API资源状态失败: %w", err)
	}

	return nil
}

// toAPIResourceResponse 转换为响应格式
func (s *APIResourceApplicationService) toAPIResourceResponse(apiResource *entity.APIResource) *dto.APIResourceResponse {
	return &dto.APIResourceResponse{
		ID:            apiResource.ID,
		TenantID:      apiResource.TenantID,
		InternalAppID: apiResource.InternalAppID,
		Name:          apiResource.Name,
		DisplayName:   apiResource.DisplayName,
		Description:   apiResource.Description,
		PermissionID:  apiResource.PermissionID,
		ServiceName:   apiResource.ServiceName,
		APIMethod:     apiResource.APIMethod,
		Path:          apiResource.Path,
		RequestType:   apiResource.RequestType,
		ResponseType:  apiResource.ResponseType,
		ContentType:   apiResource.ContentType,
		IsPublic:      apiResource.IsPublic,
		Status:        apiResource.Status,
		CreatedAt:     apiResource.CreatedAt,
		UpdatedAt:     apiResource.UpdatedAt,
	}
}