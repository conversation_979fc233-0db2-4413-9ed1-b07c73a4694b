package service

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// ResourceAPIRelationApplicationService 页面与API关联关系应用服务
type ResourceAPIRelationApplicationService struct {
	relationRepo     repository.ResourceAPIRelationRepository
	resourceRepo     repository.ResourceRepository
	apiResourceRepo  repository.APIResourceRepository
	baseService      *BaseService
}

// NewResourceAPIRelationApplicationService 创建页面与API关联关系应用服务实例
func NewResourceAPIRelationApplicationService(
	relationRepo repository.ResourceAPIRelationRepository,
	resourceRepo repository.ResourceRepository,
	apiResourceRepo repository.APIResourceRepository,
) *ResourceAPIRelationApplicationService {
	return &ResourceAPIRelationApplicationService{
		relationRepo:    relationRepo,
		resourceRepo:    resourceRepo,
		apiResourceRepo: apiResourceRepo,
		baseService:     NewBaseService(),
	}
}

// CreateRelation 创建页面与API关联关系
func (s *ResourceAPIRelationApplicationService) CreateRelation(ctx context.Context, req *dto.CreateResourceAPIRelationRequest) (*dto.ResourceAPIRelationResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 验证页面资源是否存在
	_, err := s.resourceRepo.FindByID(ctx, req.ResourceID)
	if err != nil {
		return nil, fmt.Errorf("页面资源不存在: %w", err)
	}

	// 验证API资源是否存在
	_, err = s.apiResourceRepo.GetByIDWithTenant(ctx, req.APIResourceID, appInfo.TenantID)
	if err != nil {
		return nil, fmt.Errorf("API资源不存在: %w", err)
	}

	// 检查关联关系是否已存在
	exists, err := s.relationRepo.Exists(ctx, req.ResourceID, req.APIResourceID)
	if err != nil {
		return nil, fmt.Errorf("检查关联关系失败: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("页面与API关联关系已存在")
	}

	// 创建关联关系实体
	relation := &entity.ResourceAPIRelation{
		TenantID:      appInfo.TenantID,
		InternalAppID: appInfo.InternalAppId,
		ResourceID:    req.ResourceID,
		APIResourceID: req.APIResourceID,
		IsRequired:    req.IsRequired,
		Description:   req.Description,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 验证实体数据
	if err := relation.Validate(); err != nil {
		return nil, fmt.Errorf("关联关系数据验证失败: %w", err)
	}

	// 保存到数据库
	if err := s.relationRepo.Create(ctx, relation); err != nil {
		return nil, fmt.Errorf("创建页面与API关联关系失败: %w", err)
	}

	return s.toResourceAPIRelationResponse(relation), nil
}

// DeleteRelation 删除页面与API关联关系
func (s *ResourceAPIRelationApplicationService) DeleteRelation(ctx context.Context, resourceID, apiResourceID int64) error {
	// 删除关联关系
	if err := s.relationRepo.Delete(ctx, resourceID, apiResourceID); err != nil {
		return fmt.Errorf("删除页面与API关联关系失败: %w", err)
	}

	return nil
}

// GetResourceAPIs 获取页面资源关联的API列表
func (s *ResourceAPIRelationApplicationService) GetResourceAPIs(ctx context.Context, resourceID int64) ([]*dto.APIResourceResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 验证页面资源是否存在
	_, err := s.resourceRepo.FindByID(ctx, resourceID)
	if err != nil {
		return nil, fmt.Errorf("页面资源不存在: %w", err)
	}

	// 获取关联关系
	relations, err := s.relationRepo.GetByResourceID(ctx, resourceID)
	if err != nil {
		return nil, fmt.Errorf("获取页面关联的API失败: %w", err)
	}

	if len(relations) == 0 {
		return []*dto.APIResourceResponse{}, nil
	}

	// 获取API资源IDs
	apiResourceIDs := make([]int64, len(relations))
	for i, relation := range relations {
		apiResourceIDs[i] = relation.APIResourceID
	}

	// 批量获取API资源
	apiResources := make([]*entity.APIResource, 0)
	for _, apiResourceID := range apiResourceIDs {
		apiResource, err := s.apiResourceRepo.GetByIDWithTenant(ctx, apiResourceID, appInfo.TenantID)
		if err != nil {
			// 跳过不存在或无权限的API资源
			continue
		}
		apiResources = append(apiResources, apiResource)
	}

	// 转换为响应格式
	responses := make([]*dto.APIResourceResponse, len(apiResources))
	for i, apiResource := range apiResources {
		responses[i] = s.toAPIResourceResponse(apiResource)
	}

	return responses, nil
}

// GetAPIResources 获取API资源关联的页面列表
func (s *ResourceAPIRelationApplicationService) GetAPIResources(ctx context.Context, apiResourceID int64) ([]*dto.ResourceResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 验证API资源是否存在
	_, err := s.apiResourceRepo.GetByIDWithTenant(ctx, apiResourceID, appInfo.TenantID)
	if err != nil {
		return nil, fmt.Errorf("API资源不存在: %w", err)
	}

	// 获取关联关系
	relations, err := s.relationRepo.GetByAPIResourceID(ctx, apiResourceID)
	if err != nil {
		return nil, fmt.Errorf("获取API关联的页面失败: %w", err)
	}

	if len(relations) == 0 {
		return []*dto.ResourceResponse{}, nil
	}

	// 获取页面资源IDs
	resourceIDs := make([]int64, len(relations))
	for i, relation := range relations {
		resourceIDs[i] = relation.ResourceID
	}

	// 批量获取页面资源
	resources := make([]*entity.Resource, 0)
	for _, resourceID := range resourceIDs {
		resource, err := s.resourceRepo.FindByID(ctx, resourceID)
		if err != nil {
			// 跳过不存在或无权限的页面资源
			continue
		}
		resources = append(resources, resource)
	}

	// 转换为响应格式
	responses := make([]*dto.ResourceResponse, len(resources))
	for i, resource := range resources {
		responses[i] = s.toResourceResponse(resource)
	}

	return responses, nil
}

// BatchAssignAPIsToResource 批量分配API给页面资源
func (s *ResourceAPIRelationApplicationService) BatchAssignAPIsToResource(ctx context.Context, req *dto.BatchAssignAPIsToResourceRequest) error {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return fmt.Errorf("应用上下文不存在")
	}

	// 验证页面资源是否存在
	_, err := s.resourceRepo.FindByID(ctx, req.ResourceID)
	if err != nil {
		return fmt.Errorf("页面资源不存在: %w", err)
	}

	// 验证所有API资源是否存在
	for _, apiResourceID := range req.APIResourceIDs {
		_, err := s.apiResourceRepo.GetByIDWithTenant(ctx, apiResourceID, appInfo.TenantID)
		if err != nil {
			return fmt.Errorf("API资源%d不存在: %w", apiResourceID, err)
		}
	}

	// 替换页面资源的所有API关联
	if err := s.relationRepo.ReplaceResourceAPIs(ctx, req.ResourceID, req.APIResourceIDs); err != nil {
		return fmt.Errorf("批量分配API给页面资源失败: %w", err)
	}

	return nil
}

// ListResourcesWithAPIs 列表查询页面资源及其关联的API
func (s *ResourceAPIRelationApplicationService) ListResourcesWithAPIs(ctx context.Context, req *dto.ListResourcesWithAPIsRequest) (*dto.ResourceWithAPIsListResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 构建查询参数
	params := &repository.ResourceAPIQueryParams{
		TenantID:      appInfo.TenantID,
		InternalAppID: req.InternalAppID,
		ResourceID:    req.ResourceID,
		ResourceType:  req.ResourceType,
		Page:          req.Page,
		PageSize:      req.PageSize,
	}

	if req.InternalAppID == 0 {
		params.InternalAppID = appInfo.InternalAppId
	}

	params.SetDefaults()

	// 查询页面资源及其API
	resourcesWithAPIs, err := s.relationRepo.GetResourcesWithAPIs(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("查询页面资源及其API失败: %w", err)
	}

	// 转换为响应格式
	items := make([]*dto.ResourceWithAPIsResponse, len(resourcesWithAPIs))
	for i, item := range resourcesWithAPIs {
		resourceResp := s.toResourceResponse(item.Resource)
		
		apiResourceResps := make([]*dto.APIResourceResponse, len(item.APIResources))
		for j, apiResource := range item.APIResources {
			apiResourceResps[j] = s.toAPIResourceResponse(apiResource)
		}
		
		relationResps := make([]*dto.ResourceAPIRelationResponse, len(item.Relations))
		for j, relation := range item.Relations {
			relationResps[j] = s.toResourceAPIRelationResponse(relation)
		}
		
		items[i] = &dto.ResourceWithAPIsResponse{
			Resource:     resourceResp,
			APIResources: apiResourceResps,
			Relations:    relationResps,
		}
	}

	// 计算总数和总页数
	total := int64(len(items))
	totalPages := int((total + int64(params.PageSize) - 1) / int64(params.PageSize))

	return &dto.ResourceWithAPIsListResponse{
		Items:      items,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// ListAPIsWithResources 列表查询API资源及其关联的页面
func (s *ResourceAPIRelationApplicationService) ListAPIsWithResources(ctx context.Context, req *dto.ListAPIsWithResourcesRequest) (*dto.APIResourceWithResourcesListResponse, error) {
	// 获取用户上下文
	_, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("用户上下文不存在")
	}
	appInfo, ok := usercontext.GetAppInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("应用上下文不存在")
	}

	// 构建查询参数
	params := &repository.APIResourceWithResourcesQueryParams{
		TenantID:       appInfo.TenantID,
		InternalAppID:  req.InternalAppID,
		APIResourceID:  req.APIResourceID,
		ServiceName:    req.ServiceName,
		APIMethod:      req.APIMethod,
		Page:           req.Page,
		PageSize:       req.PageSize,
	}

	if req.InternalAppID == 0 {
		params.InternalAppID = appInfo.InternalAppId
	}

	params.SetDefaults()

	// 查询API资源及其页面
	apisWithResources, err := s.relationRepo.GetAPIsWithResources(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("查询API资源及其页面失败: %w", err)
	}

	// 转换为响应格式
	items := make([]*dto.APIResourceWithResourcesResponse, len(apisWithResources))
	for i, item := range apisWithResources {
		apiResourceResp := s.toAPIResourceResponse(item.APIResource)
		
		resourceResps := make([]*dto.ResourceResponse, len(item.Resources))
		for j, resource := range item.Resources {
			resourceResps[j] = s.toResourceResponse(resource)
		}
		
		relationResps := make([]*dto.ResourceAPIRelationResponse, len(item.Relations))
		for j, relation := range item.Relations {
			relationResps[j] = s.toResourceAPIRelationResponse(relation)
		}
		
		items[i] = &dto.APIResourceWithResourcesResponse{
			APIResource: apiResourceResp,
			Resources:   resourceResps,
			Relations:   relationResps,
		}
	}

	// 计算总数和总页数
	total := int64(len(items))
	totalPages := int((total + int64(params.PageSize) - 1) / int64(params.PageSize))

	return &dto.APIResourceWithResourcesListResponse{
		Items:      items,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}, nil
}

// toResourceAPIRelationResponse 转换为关联关系响应格式
func (s *ResourceAPIRelationApplicationService) toResourceAPIRelationResponse(relation *entity.ResourceAPIRelation) *dto.ResourceAPIRelationResponse {
	return &dto.ResourceAPIRelationResponse{
		ID:            relation.ID,
		TenantID:      relation.TenantID,
		InternalAppID: relation.InternalAppID,
		ResourceID:    relation.ResourceID,
		APIResourceID: relation.APIResourceID,
		IsRequired:    relation.IsRequired,
		Description:   relation.Description,
		CreatedAt:     relation.CreatedAt,
		UpdatedAt:     relation.UpdatedAt,
	}
}

// toAPIResourceResponse 转换为API资源响应格式
func (s *ResourceAPIRelationApplicationService) toAPIResourceResponse(apiResource *entity.APIResource) *dto.APIResourceResponse {
	return &dto.APIResourceResponse{
		ID:            apiResource.ID,
		TenantID:      apiResource.TenantID,
		InternalAppID: apiResource.InternalAppID,
		Name:          apiResource.Name,
		DisplayName:   apiResource.DisplayName,
		Description:   apiResource.Description,
		PermissionID:  apiResource.PermissionID,
		ServiceName:   apiResource.ServiceName,
		APIMethod:     apiResource.APIMethod,
		Path:          apiResource.Path,
		RequestType:   apiResource.RequestType,
		ResponseType:  apiResource.ResponseType,
		ContentType:   apiResource.ContentType,
		IsPublic:      apiResource.IsPublic,
		Status:        apiResource.Status,
		CreatedAt:     apiResource.CreatedAt,
		UpdatedAt:     apiResource.UpdatedAt,
	}
}

// toResourceResponse 转换为页面资源响应格式
func (s *ResourceAPIRelationApplicationService) toResourceResponse(resource *entity.Resource) *dto.ResourceResponse {
	return &dto.ResourceResponse{
		ID:            resource.ID,
		TenantID:      resource.TenantID,
		InternalAppID: resource.InternalAppID,
		Name:          resource.Name,
		DisplayName:   resource.DisplayName,
		Description:   resource.Description,
		ResourceType:  string(resource.ResourceType),
		PermissionID:  resource.PermissionID,
		ParentID:      resource.ParentID,
		Path:          resource.Path,
		Icon:          resource.Icon,
		SortOrder:     resource.SortOrder,
		IsSystem:      resource.IsSystem,
		CreatedAt:     resource.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     resource.UpdatedAt.Format("2006-01-02 15:04:05"),
		IsPublic:      resource.IsPublic,
		PublicLevel:   string(resource.PublicLevel),
		Assignable:    resource.Assignable,
	}
}