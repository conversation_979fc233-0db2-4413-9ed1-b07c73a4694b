# HTTP 接口权限拦截系统设计方案

## 1. 系统概述

基于新的资源API分离架构，实现 HTTP 接口的权限拦截机制。系统采用独立的 `api_resource` 表管理API资源，通过 `resource_api_relations` 表实现页面与API的多对多关联，支持API资源的复用和灵活的权限配置。系统支持API资源的独立权限管理，也支持从关联页面继承权限，并通过内存缓存优化性能。

## 2. 新架构系统分析

### 2.1 资源分离架构

基于新的资源API分离设计，系统采用以下架构：

#### 2.1.1 Resource 实体结构（页面资源）
- **基础属性**：ID、名称、显示名称、描述、资源类型
- **权限控制**：permission_id 关联权限表，支持独立权限配置
- **层级关系**：parent_id 支持资源父子关系
- **访问控制**：is_public、public_level、assignable 控制访问权限
- **多租户**：tenant_id、internal_app_id 支持多租户隔离

#### 2.1.2 APIResource 实体结构（API资源）
- **基础属性**：ID、名称、显示名称、描述
- **API 属性**：服务名称、HTTP方法、请求/响应类型、ContentType、路径
- **权限控制**：permission_id 关联权限表，支持独立权限配置
- **访问控制**：is_public 控制公开访问
- **多租户**：tenant_id、internal_app_id 支持多租户隔离

#### 2.1.3 资源类型支持
- **Resource表**：menu（菜单）、page（页面）、button（按钮）
- **APIResource表**：独立管理所有API接口资源

#### 2.1.4 关联关系
- **多对多关联**：通过 `resource_api_relations` 表实现页面与API的多对多关联
- **API复用**：一个API可以被多个页面使用
- **灵活权限**：API可以有独立权限，也可以继承关联页面权限

### 2.2 新的关联表设计

采用新的 `resource_api_relations` 表实现页面与API的关联：

```sql
CREATE TABLE `resource_api_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `resource_id` bigint NOT NULL COMMENT '页面资源ID',
  `api_resource_id` bigint NOT NULL COMMENT 'API资源ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resource_api_tenant` (`resource_id`),
  KEY `idx_resource_api_api_id` (`api_resource_id`),
  UNIQUE KEY `uk_resource_api_relation` (`resource_id`, `api_resource_id`),
  CONSTRAINT `fk_resource_api_resource_id` FOREIGN KEY (`resource_id`) REFERENCES `resource` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_resource_api_api_resource_id` FOREIGN KEY (`api_resource_id`) REFERENCES `api_resource` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面与API关联表';
```

#### 关联表特点

- **简化设计**：移除了租户ID和应用ID字段，通过外键关联获取
- **多对多关联**：支持一个API被多个页面使用，一个页面关联多个API
- **权限继承**：API资源可以有独立权限，也可以继承关联页面的权限

### 2.3 权限管理机制

- **多租户隔离**：resource和api_resource表都包含 `tenant_id` 字段
- **应用级隔离**：通过 `internal_app_id` 区分不同应用
- **资源层级**：通过 `parent_id` 支持父子关系（仅限页面资源）
- **权限检查**：基于用户-角色-权限的 RBAC 模型
- **独立权限**：API资源和页面资源都可以配置独立的 `permission_id`
- **权限继承**：API资源可以继承关联页面的权限
- **公开访问**：支持匿名访问、认证访问等多种级别

## 3. API与页面关联关系设计

### 3.1 新的关联关系模型

```
┌─────────────────┐    resource_api_relations    ┌─────────────────┐
│   Page Resource │ ◄─────────────────────────► │  API Resource   │
│                 │                              │                 │
│ - resource_type │                              │ - name          │
│   = "page"      │                              │ - path          │
│ - permission_id │                              │ - api_method    │
│ - parent_id     │                              │ - permission_id │
└─────────────────┘                              └─────────────────┘
         │                                                │
         │                                                │
         ▼                                                ▼
┌─────────────────┐                              ┌─────────────────┐
│ Page Permission │                              │ API Permission  │
│                 │                              │                 │
│ - view          │                              │ - read          │
│ - edit          │                              │ - write         │
│ - delete        │                              │ - admin         │
└─────────────────┘                              └─────────────────┘
```

### 3.2 关联策略

#### 3.2.1 多对多关联
- 一个 API 可以被多个页面使用（API复用）
- 一个页面可以关联多个API（功能完整性）
- 通过 `resource_api_relations` 表记录关联关系

#### 3.2.2 权限配置策略
```sql
-- 示例：用户管理API的权限配置
-- API资源：获取用户列表
INSERT INTO api_resource (
    id, tenant_id, internal_app_id, name, permission_id,
    service_name, api_method, path
) VALUES
(200, 1, 1, 'list-users-api', 1001, 'user-service', 'GET', '/api/users');

-- 页面资源：用户管理页面
INSERT INTO resource (
    id, tenant_id, internal_app_id, name, resource_type, permission_id
) VALUES
(100, 1, 1, 'user-management-page', 'page', 1002);

-- 关联关系：页面使用API
INSERT INTO resource_api_relations (resource_id, api_resource_id)
VALUES (100, 200);
```

### 3.3 关联关系展示

在新架构中，页面资源和API资源分别管理，关联关系通过独立的接口查询：

```json
{
  "page_resource": {
    "id": 100,
    "name": "user-management-page",
    "display_name": "用户管理",
    "resource_type": "page",
    "permission_id": 1002
  },
  "associated_apis": [
    {
      "id": 200,
      "name": "list-users-api",
      "display_name": "获取用户列表",
      "api_method": "GET",
      "path": "/api/users",
      "permission_id": 1001,
      "service_name": "user-service"
    },
    {
      "id": 201,
      "name": "create-user-api",
      "display_name": "创建用户",
      "api_method": "POST",
      "path": "/api/users",
      "permission_id": 1003,
      "service_name": "user-service"
    }
  ],
  "relation_info": {
    "total_apis": 2,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## 4. 权限拦截架构设计

## 4. 权限拦截架构设计

### 4.1 核心组件

```
┌─────────────────────┐
│   HTTP Request      │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ Permission          │
│ Interceptor         │
│ Middleware          │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ API Resource        │
│ Matcher             │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ Resource Relation   │
│ Permission Resolver │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ Permission          │
│ Cache Manager       │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ Permission          │
│ Checker             │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ Response            │
│ (Allow/Deny)        │
└─────────────────────┘
```

### 4.2 新架构的拦截流程

1. **API资源匹配**：根据请求 URL 和 HTTP 方法在 `api_resource` 表中查找对应的 API 资源
2. **页面上下文识别**：通过 Header 或 Session 获取当前页面上下文
3. **关联关系解析**：查询 `resource_api_relations` 表获取 API 与页面的关联关系
4. **权限检查策略**：
   - 优先检查API资源的独立权限（`api_resource.permission_id`）
   - 如果API没有独立权限，检查关联页面的权限（`resource.permission_id`）
   - 支持多页面关联时的权限合并检查
5. **公开访问检查**：检查API资源是否为公开资源（`api_resource.is_public`）
6. **用户认证检查**：验证用户是否已登录
7. **权限验证**：检查用户是否拥有所需权限
8. **租户隔离检查**：确保资源访问符合租户隔离要求
9. **结果返回**：允许访问或返回错误

## 5. 核心实现组件

### 5.1 API 资源匹配器 (API Resource Matcher)

```go
type APIResourceMatcher interface {
    MatchAPIResource(ctx context.Context, path string, method string, tenantID, internalAppID int64) (*entity.APIResource, error)
    GetResourceRelations(ctx context.Context, apiResourceID int64) ([]*entity.ResourceAPIRelation, error)
    GetRelatedPageResources(ctx context.Context, apiResourceID int64) ([]*entity.Resource, error)
}

type APIResourceMatcherImpl struct {
    apiResourceRepo  repository.APIResourceRepository
    relationRepo     repository.ResourceAPIRelationRepository
    resourceRepo     repository.ResourceRepository
    cache           *APIResourceCache
    pathTrie        *APIPathTrie
}
```

### 5.2 API权限解析器 (API Permission Resolver)

```go
type APIPermissionResolver interface {
    ResolveAPIPermissions(ctx context.Context, apiResource *entity.APIResource) ([]*ResolvedPermission, error)
    ResolveRelatedPagePermissions(ctx context.Context, apiResourceID int64) ([]*ResolvedPermission, error)
    GetEffectivePermissions(ctx context.Context, apiResourceID int64, pageContext string) ([]*ResolvedPermission, error)
}

type ResolvedPermission struct {
    PermissionID     int64  `json:"permission_id"`
    PermissionName   string `json:"permission_name"`
    PermissionCode   string `json:"permission_code"`
    Source          string `json:"source"` // "api" or "page"
    PageResourceID  *int64 `json:"page_resource_id,omitempty"`
    PageName        string `json:"page_name,omitempty"`
}
```

### 5.3 新架构权限检查器

```go
type APIPermissionChecker interface {
    CheckAPIAccess(ctx context.Context, userID int64, apiResource *entity.APIResource, pageContext string) (bool, error)
    CheckResolvedPermissions(ctx context.Context, userID int64, permissions []*ResolvedPermission) (bool, error)
    ValidatePageContext(ctx context.Context, userID int64, pageResourceID int64) (bool, error)
    CheckAPIResourcePermission(ctx context.Context, userID int64, apiResourceID int64) (bool, error)
}

type APIPermissionCheckerImpl struct {
    permissionService    service.PermissionCheckService
    permissionResolver   APIPermissionResolver
    relationRepo        repository.ResourceAPIRelationRepository
    cache               *PermissionCache
}
```

### 5.4 页面上下文管理器

```go
type PageContextManager interface {
    GetPageContext(c *gin.Context) (*PageContext, error)
    ValidatePageAccess(ctx context.Context, userID int64, pageContext *PageContext) (bool, error)
}

type PageContext struct {
    PageResourceID int64  `json:"page_resource_id"`
    PagePath       string `json:"page_path"`
    UserRole       string `json:"user_role"`
    AccessLevel    string `json:"access_level"`
}

// 从请求中获取页面上下文的方式
func (pcm *PageContextManagerImpl) GetPageContext(c *gin.Context) (*PageContext, error) {
    // 从gin的访问url获取当前访问地址
    
    return &PageContext{
        PageResourceID: parseID(pageID),
        PagePath:       pagePath,
    }, nil
}
```

### 5.5 新架构权限拦截中间件

```go
func APIPermissionInterceptor(config *APIPermissionConfig) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 预处理检查
        if shouldSkip(c, config) {
            c.Next()
            return
        }

        // 2. 获取用户和应用信息
        userInfo, _ := usercontext.GetUserInfo(c.Request.Context())
        appInfo, _ := usercontext.GetAppInfo(c.Request.Context())

        // 3. 匹配 API 资源
        apiResource, err := apiMatcher.MatchAPIResource(c.Request.Context(),
            c.Request.URL.Path, c.Request.Method, appInfo.TenantID, appInfo.InternalAppId)

        if err != nil || apiResource == nil {
            handleResourceNotFound(c, config)
            return
        }

        // 4. 获取页面上下文
        pageContext, err := contextManager.GetPageContext(c)
        if err != nil {
            c.Set("page_context_error", err.Error())
            // 继续执行，使用默认权限检查
        }

        // 5. 公开访问检查
        if apiResource.IsPublic && checkPublicAccess(c.Request.Context(), apiResource, c.Request) {
            c.Next()
            return
        }

        // 6. 认证检查
        if userInfo == nil {
            commonResponse.Unauthorized(c, "authentication required")
            return
        }

        // 7. API权限检查
        var hasAccess bool

        // 优先检查API资源的独立权限
        if apiResource.PermissionID != nil {
            hasAccess, err = apiChecker.CheckAPIResourcePermission(c.Request.Context(),
                userInfo.UserID, apiResource.ID)
        } else {
            // API没有独立权限，检查关联页面权限
            if pageContext != nil && pageContext.PageResourceID > 0 {
                // 检查用户是否有页面访问权限
                pageAccess, _ := contextManager.ValidatePageAccess(c.Request.Context(),
                    userInfo.UserID, pageContext)
                if !pageAccess {
                    commonResponse.Forbidden(c, "page access denied")
                    return
                }
                hasAccess = true // 有页面权限即可访问API
            } else {
                // 检查所有关联页面的权限（任一页面有权限即可）
                relatedPages, _ := apiMatcher.GetRelatedPageResources(c.Request.Context(), apiResource.ID)
                for _, page := range relatedPages {
                    if page.PermissionID != nil {
                        pageAccess, _ := permissionService.CheckUserPermission(c.Request.Context(),
                            userInfo.UserID, *page.PermissionID)
                        if pageAccess {
                            hasAccess = true
                            break
                        }
                    }
                }
            }
        }

        if err != nil {
            commonResponse.InternalError(c, nil)
            return
        }

        if !hasAccess {
            commonResponse.Forbidden(c, "insufficient permissions")
            return
        }

        // 8. 将上下文信息传递给后续处理器
        if pageContext != nil {
            c.Set("page_context", pageContext)
        }
        c.Set("api_resource", apiResource)

        c.Next()
    }
}
```

## 6. 内存缓存方案设计

### 6.1 新架构缓存设计

```
┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────┐
│  API Resource Cache │  │Resource API Relation│  │ API Permission      │  │  User Role Cache    │
│                     │  │Cache                │  │ Cache               │  │                     │
│Key: path+method+app │  │Key: api_id          │  │Key: user+api        │  │ Key: user+app       │
│Value: APIResource   │  │Value: Relations     │  │Value: bool          │  │ Value: []Role       │
│TTL: 2h              │  │TTL: 1h              │  │TTL: 30m             │  │ TTL: 30m            │
└─────────────────────┘  └─────────────────────┘  └─────────────────────┘  └─────────────────────┘
          │                        │                        │                        │
          └────────────────────────┼────────────────────────┼────────────────────────┘
                                   │                        │
                         ┌─────────▼────────────────────────▼───────────┐
                         │       API Permission Cache Manager           │
                         │                                               │
                         │ - Multi-layer LRU Eviction                   │
                         │ - API Resource Focused Caching               │
                         │ - Intelligent Refresh Strategy               │
                         │ - Cascade Invalidation                       │
                         │ - Performance Metrics Collection             │
                         └───────────────────────────────────────────────┘
```

### 6.2 新架构缓存实现

```go
type APIPermissionCacheConfig struct {
    MaxMemoryMB          int           // 最大内存使用（MB）
    APIResourceTTL       time.Duration // API资源缓存TTL
    APIRelationTTL       time.Duration // API关联关系缓存TTL
    APIPermissionTTL     time.Duration // API权限缓存TTL
    UserRoleTTL          time.Duration // 用户角色缓存TTL
    CleanupInterval      time.Duration // 清理间隔
    MaxEntries           int           // 最大条目数
    EnableCascadeInvalid bool          // 启用级联失效
}

type APIPermissionCacheManager struct {
    apiResourceCache      *LRUCache[string, *entity.APIResource]
    apiRelationCache      *LRUCache[string, []*entity.ResourceAPIRelation]
    apiPermissionCache    *LRUCache[string, bool]
    userRoleCache        *LRUCache[string, []entity.Role]
    config               *APIPermissionCacheConfig
    stats                *APIPermissionCacheStats
    mutex                sync.RWMutex
}

// 新架构的缓存key生成策略
func (c *APIPermissionCacheManager) buildAPIResourceKey(path, method string, tenantID, appID int64) string {
    return fmt.Sprintf("api_res:%d:%d:%s:%s", tenantID, appID, method, path)
}

func (c *APIPermissionCacheManager) buildAPIRelationKey(apiResourceID int64) string {
    return fmt.Sprintf("api_rel:%d", apiResourceID)
}

func (c *APIPermissionCacheManager) buildAPIPermissionKey(userID, apiResourceID int64) string {
    return fmt.Sprintf("api_perm:%d:%d", userID, apiResourceID)
}

func (c *APIPermissionCacheManager) buildUserRoleKey(userID, appID int64) string {
    return fmt.Sprintf("user_role:%d:%d", userID, appID)
}
```

### 6.3 新架构级联失效机制

```go
// 新架构级联失效策略
func (c *APIPermissionCacheManager) InvalidateRelatedCaches(resourceType string, resourceID int64) {
    switch resourceType {
    case "api_resource":
        // API资源变更，失效相关的关联关系缓存和权限缓存
        c.invalidateAPIResourceRelatedCaches(resourceID)
    case "page_resource":
        // 页面资源变更，失效相关的关联关系缓存和权限缓存
        c.invalidatePageResourceRelatedCaches(resourceID)
    case "api_relation":
        // API关联关系变更，失效相关的权限缓存
        c.invalidateAPIRelationCaches(resourceID)
    case "user_role":
        // 用户角色变更，失效用户相关的所有权限缓存
        c.invalidateUserRelatedCaches(resourceID)
    case "permission":
        // 权限变更，失效相关的API权限缓存
        c.invalidatePermissionRelatedCaches(resourceID)
    }
}

func (c *APIPermissionCacheManager) invalidateAPIResourceRelatedCaches(apiResourceID int64) {
    c.mutex.Lock()
    defer c.mutex.Unlock()

    // 删除API关联关系缓存
    c.apiRelationCache.Remove(fmt.Sprintf("api_rel:%d", apiResourceID))

    // 删除API权限缓存中涉及该API资源的所有缓存项
    c.apiPermissionCache.RemoveByPrefix(fmt.Sprintf("api_perm:*:%d", apiResourceID))

    c.stats.CascadeInvalidations++
}

func (c *APIPermissionCacheManager) invalidatePermissionRelatedCaches(permissionID int64) {
    c.mutex.Lock()
    defer c.mutex.Unlock()

    // 权限变更时，需要失效所有相关的API权限缓存
    // 这里可以通过查询数据库找到使用该权限的API资源，然后失效相关缓存
    c.apiPermissionCache.Clear() // 简化处理，清空所有API权限缓存

    c.stats.CascadeInvalidations++
}
```

### 6.4 内存占用优化

#### 6.4.1 新架构紧凑数据结构

```go
// 紧凑的API资源缓存结构
type CompactAPIResource struct {
    ID              int64
    Name            string
    Path            string
    Method          string
    PermissionID    *int64
    IsPublic        bool
    ServiceName     string
    TenantID        int64
    InternalAppID   int64
}

// 紧凑的API关联关系结构
type CompactAPIRelation struct {
    ID              int64
    ResourceID      int64
    APIResourceID   int64
    CreatedAt       int64    // 使用时间戳而非time.Time
}

// 使用位图优化API权限存储
type APIPermissionBitmap struct {
    UserID          int64
    APIResourceID   int64
    HasPermission   bool
    PermissionBits  uint64   // 使用位图压缩权限数据
    UpdateTime      int64
}
```

#### 6.4.2 智能预加载

```go
type SmartPreloader struct {
    cacheManager     *EnhancedPermissionCacheManager
    accessStats      *AccessStatsCollector
    preloadScheduler *time.Ticker
}

// 基于访问统计的智能预加载
func (sp *SmartPreloader) PreloadHotResources() {
    hotResources := sp.accessStats.GetHotResources(100) // 获取最热门的100个资源，开放和系统资源优先
    
    for _, resource := range hotResources {
        // 预加载资源关系
        relations, _ := sp.loadResourceRelations(resource.ID)
        sp.cacheManager.SetResourceRelations(resource.ID, relations)
        
        // 预加载高频用户的权限
        hotUsers := sp.accessStats.GetHotUsersForResource(resource.ID, 50)
        for _, userID := range hotUsers {
            permissions := sp.loadUserPermissions(userID, resource.ID)
            sp.cacheManager.SetUserPermissions(userID, resource.ID, permissions)
        }
    }
}
```

### 6.5 性能监控与统计

```go
type EnhancedCacheStats struct {
    // 基础指标
    RequestTotal        int64   `json:"request_total"`
    CacheHitRate       float64 `json:"cache_hit_rate"`
    MemoryUsageBytes   int64   `json:"memory_usage_bytes"`
    
    // 分层指标
    APIResourceHitRate    float64 `json:"api_resource_hit_rate"`
    RelationHitRate      float64 `json:"relation_hit_rate"`
    ContextualPermHitRate float64 `json:"contextual_perm_hit_rate"`
    UserRoleHitRate      float64 `json:"user_role_hit_rate"`
    
    // 操作指标
    CascadeInvalidations int64   `json:"cascade_invalidations"`
    PreloadOperations   int64   `json:"preload_operations"`
    EvictionCount       int64   `json:"eviction_count"`
    
    // 性能指标
    AvgLookupTime       float64 `json:"avg_lookup_time_ms"`
    P95LookupTime       float64 `json:"p95_lookup_time_ms"`
    P99LookupTime       float64 `json:"p99_lookup_time_ms"`
}
```

### 6.6 内存容量规划

#### 6.6.1 新架构容量估算

```
假设场景（新架构）：
- API资源数量：5,000 条
- 页面资源数量：2,000 条
- API关联关系数量：10,000 条（平均每个API被2个页面使用）
- 活跃用户：10,000 人
- API权限记录：50,000 条（每用户平均5个常用API权限）

内存占用估算：
- API资源缓存：5,000 × 120B = 600KB
- API关联关系缓存：10,000 × 60B = 600KB
- API权限缓存：50,000 × 32B = 1.6MB
- 用户角色缓存：10,000 × 100B = 1MB

总计：约 3.8MB（加上 LRU 开销和索引约 6MB）
相比原方案节省约80%内存占用，架构更清晰
```

### 6.7 新架构缓存预热策略

```go
type APICacheWarmupManager struct {
    cacheManager       *APIPermissionCacheManager
    apiResourceRepo    repository.APIResourceRepository
    relationRepo       repository.ResourceAPIRelationRepository
    permissionRepo     repository.PermissionRepository
}

// 应用启动时的缓存预热
func (cwm *APICacheWarmupManager) WarmupOnStartup() error {
    // 1. 预加载所有API资源
    if err := cwm.preloadAPIResources(); err != nil {
        return err
    }

    // 2. 预加载热门API关联关系
    if err := cwm.preloadHotAPIRelations(); err != nil {
        return err
    }

    // 3. 预加载系统管理员的API权限
    if err := cwm.preloadAdminAPIPermissions(); err != nil {
        return err
    }

    return nil
}

func (cwm *APICacheWarmupManager) preloadAPIResources() error {
    apiResources, err := cwm.apiResourceRepo.GetAll(context.Background())
    if err != nil {
        return err
    }

    for _, apiResource := range apiResources {
        key := cwm.cacheManager.buildAPIResourceKey(apiResource.Path,
            apiResource.APIMethod, apiResource.TenantID, apiResource.InternalAppID)
        cwm.cacheManager.apiResourceCache.Set(key, &apiResource,
            cwm.cacheManager.config.APIResourceTTL)
    }

    return nil
}

func (cwm *APICacheWarmupManager) preloadHotAPIRelations() error {
    // 预加载使用频率高的API关联关系
    hotAPIResources, err := cwm.getHotAPIResources(100) // 获取最热门的100个API
    if err != nil {
        return err
    }

    for _, apiResource := range hotAPIResources {
        relations, err := cwm.relationRepo.GetByAPIResourceID(context.Background(), apiResource.ID)
        if err != nil {
            continue
        }

        key := cwm.cacheManager.buildAPIRelationKey(apiResource.ID)
        cwm.cacheManager.apiRelationCache.Set(key, relations,
            cwm.cacheManager.config.APIRelationTTL)
    }

    return nil
}
```

## 7. 管理界面集成设计(基于新架构适配)

### 7.1 新架构功能扩展

基于新的资源API分离架构，需要扩展以下能力：

#### 7.1.1 资源管理页面增强

1. **API关联管理**
   - 在页面资源详情中显示关联的API列表
   - 支持批量添加/移除API关联
   - 可视化的API关联关系图
   - 显示API的复用情况

2. **独立权限配置**
   - 为页面资源配置独立权限
   - 为API资源配置独立权限
   - 权限继承关系展示
   - 权限冲突检测和解决

#### 7.1.2 树形结构展示增强

现有的 `ResourceResponse` 结构已包含 `APIResources` 字段，需要利用此字段展示API绑定关系：

```json
{
  "id": 100,
  "name": "user-management-page",
  "display_name": "用户管理",
  "resource_type": "page",
  "api_resources": [
    {
      "id": 200,
      "name": "list-users-api",
      "display_name": "获取用户列表",
      "permission_code": "user.list.view",
      "permission_scope": "read",
      "is_required": true
    }
  ],
  "children": [
    {
      "id": 200,
      "name": "list-users-api",
      "display_name": "获取用户列表",
      "resource_type": "api",
      "bound_permissions": [
        {
          "page_name": "用户管理",
          "permission_code": "user.list.view", 
          "permission_scope": "read"
        },
        {
          "page_name": "用户报表",
          "permission_code": "user.list.report",
          "permission_scope": "read"
        }
      ]
    }
  ]
}
```

### 7.2 新增管理功能

#### 7.2.1 API绑定管理界面(已有功能，分析未实现部分进行完善)

```typescript
interface APIBindingManager {
  // 获取页面绑定的API列表
  getPageAPIs(pageResourceId: number): Promise<APIBinding[]>
  
  // 添加API绑定
  addAPIBinding(binding: CreateAPIBindingRequest): Promise<void>
  
  // 更新API绑定权限配置
  updateAPIBinding(bindingId: number, config: APIBindingConfig): Promise<void>
  
  // 移除API绑定
  removeAPIBinding(bindingId: number): Promise<void>
  
  // 批量操作
  batchUpdateAPIBindings(operations: BatchAPIBindingOperation[]): Promise<void>
}

interface APIBinding {
  id: number
  pageResourceId: number
  apiResourceId: number
  permissionCode: string
  permissionName: string
  permissionScope: 'read' | 'write' | 'delete' | 'admin'
  isRequired: boolean
  inheritParent: boolean
  description?: string
}
```

#### 7.2.2 权限配置界面

```typescript
interface PermissionConfigManager {
  // 获取API的所有绑定权限配置
  getAPIPermissionConfigs(apiResourceId: number): Promise<PermissionConfig[]>
  
  // 创建权限配置模板
  createPermissionTemplate(template: PermissionTemplate): Promise<void>
  
  // 应用权限模板到绑定关系
  applyPermissionTemplate(templateId: number, bindingIds: number[]): Promise<void>
  
  // 权限冲突检测
  detectPermissionConflicts(bindingId: number): Promise<ConflictReport>
}

interface PermissionConfig {
  bindingId: number
  pageName: string
  pageResourceId: number
  apiName: string
  apiResourceId: number
  permissionCode: string
  permissionScope: string
  isRequired: boolean
  conflicts?: string[]
}
```

### 7.3 用户界面设计

#### 7.3.1 资源树增强显示

```html
<!-- 页面资源节点 -->
<div class="page-resource-node">
  <div class="resource-header">
    <span class="resource-icon">📄</span>
    <span class="resource-name">用户管理</span>
    <span class="resource-type">页面</span>
    <button class="bind-api-btn">绑定API</button>
  </div>
  
  <!-- 绑定的API资源 -->
  <div class="bound-apis">
    <div class="api-binding" v-for="api in boundAPIs">
      <span class="api-icon">🔌</span>
      <span class="api-name">{{ api.display_name }}</span>
      <span class="permission-badge" :class="api.permission_scope">
        {{ api.permission_code }}
      </span>
      <button class="config-btn" @click="configurePermission(api)">配置</button>
    </div>
  </div>
  
  <!-- 子API资源（在树中显示） -->
  <div class="api-children">
    <div class="api-resource-node" v-for="api in apiChildren">
      <span class="api-icon">⚡</span>
      <span class="api-name">{{ api.display_name }}</span>
      <span class="api-method">{{ api.api_method }}</span>
      <span class="api-path">{{ api.path }}</span>
      
      <!-- 多页面绑定显示 -->
      <div class="binding-summary" v-if="api.bound_permissions?.length > 1">
        <span class="binding-count">绑定到 {{ api.bound_permissions.length }} 个页面</span>
        <div class="binding-details">
          <div v-for="binding in api.bound_permissions" class="binding-item">
            <span class="page-name">{{ binding.page_name }}</span>
            <span class="permission-code">{{ binding.permission_code }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### 7.3.2 权限配置弹窗

```html
<div class="permission-config-modal">
  <div class="modal-header">
    <h3>API权限配置</h3>
    <p>API: {{ apiResource.display_name }} ({{ apiResource.path }})</p>
  </div>
  
  <div class="modal-body">
    <div class="binding-configs">
      <div v-for="config in permissionConfigs" class="config-item">
        <div class="page-info">
          <h4>{{ config.pageName }}</h4>
          <p>页面路径: {{ config.pageResourceId }}</p>
        </div>
        
        <div class="permission-settings">
          <div class="form-group">
            <label>权限编码</label>
            <input v-model="config.permissionCode" type="text" />
          </div>
          
          <div class="form-group">
            <label>权限范围</label>
            <select v-model="config.permissionScope">
              <option value="read">只读</option>
              <option value="write">读写</option>
              <option value="delete">删除</option>
              <option value="admin">管理员</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="config.isRequired" />
              必需权限
            </label>
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="config.inheritParent" />
              继承父级权限
            </label>
          </div>
        </div>
        
        <div v-if="config.conflicts?.length" class="conflicts-warning">
          <h5>权限冲突警告</h5>
          <ul>
            <li v-for="conflict in config.conflicts">{{ conflict }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  
  <div class="modal-footer">
    <button @click="saveConfigurations">保存配置</button>
    <button @click="applyTemplate">应用模板</button>
    <button @click="closeModal">取消</button>
  </div>
</div>
```

### 7.4 新架构数据库结构

基于新的资源API分离架构，数据库结构如下：

```sql
-- API资源表（已创建）
CREATE TABLE `api_resource` (
  `id` bigint NOT NULL COMMENT '分布式ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID',
  `name` varchar(100) NOT NULL COMMENT 'API资源名称',
  `description` varchar(255) DEFAULT NULL COMMENT 'API资源描述',
  `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID',
  `service_name` varchar(100) NOT NULL COMMENT '应用服务名称',
  `api_method` varchar(10) NOT NULL COMMENT 'HTTP方法',
  `path` varchar(255) NOT NULL COMMENT 'API路径',
  `request_type` varchar(50) DEFAULT 'json' COMMENT '请求数据类型',
  `response_type` varchar(50) DEFAULT 'json' COMMENT '响应数据类型',
  `content_type` varchar(100) DEFAULT NULL COMMENT 'Content-Type',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开访问',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_resource_tenant_app` (`tenant_id`, `internal_app_id`),
  KEY `idx_api_resource_permission_id` (`permission_id`),
  KEY `idx_api_resource_path_method` (`path`, `api_method`),
  UNIQUE KEY `uk_api_resource_tenant_path_method` (`tenant_id`, `path`, `api_method`)
);

-- 页面与API关联表（已创建）
CREATE TABLE `resource_api_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `resource_id` bigint NOT NULL COMMENT '页面资源ID',
  `api_resource_id` bigint NOT NULL COMMENT 'API资源ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_resource_api_tenant` (`resource_id`),
  KEY `idx_resource_api_api_id` (`api_resource_id`),
  UNIQUE KEY `uk_resource_api_relation` (`resource_id`, `api_resource_id`)
);

-- 为resource表添加permission_id字段（如果还没有）
ALTER TABLE `resource`
ADD COLUMN `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID' AFTER `resource_type`,
ADD KEY `idx_resource_permission_id` (`permission_id`);
```

### 7.5 新架构API接口扩展

基于新的资源API分离架构，需要扩展以下接口：

```go
// API资源管理
POST /api/user/api-resource/create           // 创建API资源
GET  /api/user/api-resource/list             // 获取API资源列表
GET  /api/user/api-resource/:id              // 获取API资源详情
PUT  /api/user/api-resource/:id              // 更新API资源
DELETE /api/user/api-resource/:id            // 删除API资源

// API关联管理
POST /api/user/resource/:id/associate-api    // 页面关联API
DELETE /api/user/resource/:id/dissociate-api // 页面取消关联API
GET  /api/user/resource/:id/associated-apis  // 获取页面关联的API列表
GET  /api/user/api-resource/:id/usage        // 获取API被哪些页面使用

// 权限配置管理
PUT  /api/user/api-resource/:id/permission   // 配置API资源权限
PUT  /api/user/resource/:id/permission       // 配置页面资源权限
GET  /api/user/api-resource/:id/permissions  // 获取API有效权限
POST /api/user/permission/check-conflicts    // 检查权限冲突

// 批量操作
POST /api/user/resource/batch-associate-apis // 批量关联API
POST /api/user/api-resource/batch-configure  // 批量配置API权限

// 权限拦截测试
POST /api/user/permission/test-interception  // 测试权限拦截
```

### 7.6 管理界面集成设计

#### 7.6.1 现有页面分析

基于对现有系统的分析，发现以下关键页面和功能：

**现有资源管理页面 (`ResourcePage.tsx`)**：
- 已具备完整的资源树形结构管理
- 支持权限配置功能（权限配置Tab）
- 具备API功能列表管理（API功能列表Tab）
- 支持批量操作和权限模板应用

**现有权限管理页面 (`PermissionPage.tsx`)**：
- 完整的权限CRUD操作
- 权限统计和分析功能
- 支持权限层级结构管理

**现有功能入口**：
- 资源管理页面已有"分配资源"按钮
- 权限配置Tab已有"添加权限"和"批量添加"功能
- API功能列表Tab已有"选择API"功能

#### 7.6.2 集成方案设计

基于现有功能，设计HTTP接口权限拦截系统的管理界面集成：

##### 1. 资源管理页面扩展

**在现有ResourcePage基础上新增功能**：

```typescript
// 扩展现有的Tab配置
const enhancedTabs = [
  {
    key: 'basic',
    label: '基本信息',
    children: renderBasicInfoTab()
  },
  {
    key: 'api',
    label: 'API功能列表',
    children: renderApiListTab()
  },
  {
    key: 'permission',
    label: '权限配置',
    children: renderPermissionConfigTab()
  },
  // 新增：HTTP接口权限拦截配置
  {
    key: 'api-permission-binding',
    label: (
      <span>
        <SecurityScanOutlined />
        接口权限绑定
      </span>
    ),
    children: renderApiPermissionBindingTab()
  },
  // 新增：权限拦截规则配置
  {
    key: 'permission-rules',
    label: (
      <span>
        <SettingOutlined />
        拦截规则配置
      </span>
    ),
    children: renderPermissionRulesTab()
  }
];
```

##### 2. 新增接口权限绑定Tab

**功能设计**：
- 显示当前页面资源绑定的所有API
- 为每个API配置不同的权限要求
- 支持批量配置和权限模板应用
- 可视化显示API与权限的绑定关系

```typescript
const renderApiPermissionBindingTab = () => {
  if (!selectedResource) {
    return (
      <Empty
        description="请选择一个页面资源配置接口权限绑定"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return (
    <Card
      title="接口权限绑定配置"
      extra={
        <Space>
          <Button
            type="default"
            icon={<ApiOutlined />}
            onClick={() => setIsBindingApiPermissions(true)}
          >
            绑定API权限
          </Button>
          <Button
            type="primary"
            icon={<SettingOutlined />}
            onClick={() => setIsConfigPermissionTemplate(true)}
          >
            权限模板
          </Button>
          <Button
            icon={<SecurityScanOutlined />}
            onClick={() => setIsCheckingConflicts(true)}
          >
            冲突检测
          </Button>
        </Space>
      }
    >
      <Spin spinning={apiPermissionBindingLoading}>
        {/* API权限绑定列表 */}
        <Table
          dataSource={apiPermissionBindings}
          rowKey="id"
          pagination={false}
          size="small"
          columns={[
            {
              title: 'API名称',
              dataIndex: 'api_name',
              render: (text, record) => (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <ApiOutlined style={{ color: '#722ed1' }} />
                  <span>{text}</span>
                  <Tag color={record.method === 'GET' ? 'blue' : 'green'}>
                    {record.method}
                  </Tag>
                </div>
              ),
            },
            {
              title: 'API路径',
              dataIndex: 'api_path',
              render: (text) => <Text code>{text}</Text>,
            },
            {
              title: '权限要求',
              dataIndex: 'required_permissions',
              render: (permissions) => (
                <Space wrap>
                  {permissions?.map((perm: string) => (
                    <Tag key={perm} color="orange">{perm}</Tag>
                  ))}
                </Space>
              ),
            },
            {
              title: '权限模式',
              dataIndex: 'permission_mode',
              render: (mode) => (
                <Tag color={mode === 'ALL' ? 'red' : 'blue'}>
                  {mode === 'ALL' ? '需要全部' : '需要任一'}
                </Tag>
              ),
            },
            {
              title: '状态',
              dataIndex: 'status',
              render: (status) => (
                <Badge
                  status={status === 'active' ? 'success' : 'default'}
                  text={status === 'active' ? '启用' : '禁用'}
                />
              ),
            },
            {
              title: '操作',
              render: (_, record) => (
                <Space>
                  <Button
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditApiPermissionBinding(record)}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    title="确定要删除这个绑定吗？"
                    onConfirm={() => handleDeleteApiPermissionBinding(record.id)}
                  >
                    <Button
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
        />
      </Spin>
    </Card>
  );
};
```

##### 3. 新增拦截规则配置Tab

**功能设计**：
- 配置全局拦截规则
- 设置回退策略
- 配置缓存策略
- 监控和日志配置

```typescript
const renderPermissionRulesTab = () => {
  return (
    <Card title="权限拦截规则配置">
      <Tabs
        type="card"
        items={[
          {
            key: 'global-rules',
            label: '全局规则',
            children: (
              <Form
                form={globalRulesForm}
                layout="vertical"
                onFinish={handleSaveGlobalRules}
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="API资源未找到处理方式"
                      name="api_resource_not_found_mode"
                      rules={[{ required: true }]}
                    >
                      <Select>
                        <Option value="DENY">拒绝访问</Option>
                        <Option value="ALLOW">允许访问</Option>
                        <Option value="LOG_AND_ALLOW">记录日志并允许</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="页面上下文缺失处理方式"
                      name="page_context_missing_mode"
                      rules={[{ required: true }]}
                    >
                      <Select>
                        <Option value="ALLOW">允许（降级到基础权限检查）</Option>
                        <Option value="DENY">拒绝访问</Option>
                        <Option value="REQUIRE">要求页面上下文</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="权限查询失败处理方式"
                      name="permission_query_fail_mode"
                      rules={[{ required: true }]}
                    >
                      <Select>
                        <Option value="ERROR">返回错误</Option>
                        <Option value="DENY">拒绝访问</Option>
                        <Option value="ALLOW">允许访问</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="缓存失败处理方式"
                      name="cache_failure_mode"
                      rules={[{ required: true }]}
                    >
                      <Select>
                        <Option value="ALLOW">允许访问</Option>
                        <Option value="DENY">拒绝访问</Option>
                        <Option value="BYPASS">绕过缓存</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    保存全局规则
                  </Button>
                </Form.Item>
              </Form>
            ),
          },
          {
            key: 'cache-config',
            label: '缓存配置',
            children: (
              <Form
                form={cacheConfigForm}
                layout="vertical"
                onFinish={handleSaveCacheConfig}
              >
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      label="API资源缓存TTL（秒）"
                      name="api_resource_ttl"
                      rules={[{ required: true, type: 'number', min: 60 }]}
                    >
                      <InputNumber min={60} max={86400} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="资源关系缓存TTL（秒）"
                      name="resource_relation_ttl"
                      rules={[{ required: true, type: 'number', min: 60 }]}
                    >
                      <InputNumber min={60} max={86400} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="上下文权限缓存TTL（秒）"
                      name="contextual_perm_ttl"
                      rules={[{ required: true, type: 'number', min: 60 }]}
                    >
                      <InputNumber min={60} max={86400} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="最大缓存内存（MB）"
                      name="max_memory_mb"
                      rules={[{ required: true, type: 'number', min: 50 }]}
                    >
                      <InputNumber min={50} max={2048} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="启用级联失效"
                      name="enable_cascade_invalid"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    保存缓存配置
                  </Button>
                </Form.Item>
              </Form>
            ),
          },
          {
            key: 'monitoring',
            label: '监控配置',
            children: (
              <Form
                form={monitoringConfigForm}
                layout="vertical"
                onFinish={handleSaveMonitoringConfig}
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="启用性能监控"
                      name="enable_performance_monitoring"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="启用详细日志"
                      name="enable_detailed_logging"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="权限检查超时时间（毫秒）"
                      name="permission_check_timeout"
                      rules={[{ required: true, type: 'number', min: 100 }]}
                    >
                      <InputNumber min={100} max={5000} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="最大并发检查数"
                      name="max_concurrent_checks"
                      rules={[{ required: true, type: 'number', min: 10 }]}
                    >
                      <InputNumber min={10} max={1000} />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    保存监控配置
                  </Button>
                </Form.Item>
              </Form>
            ),
          },
        ]}
      />
    </Card>
  );
};
```

##### 4. 扩展现有操作按钮

**在现有按钮基础上新增功能**：

```typescript
// 扩展现有的操作按钮区域
<div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexShrink: 0 }}>
  <Space>
    <Button
      type="primary"
      icon={<PlusOutlined />}
      onClick={() => setIsAddingResource(true)}
      size="small"
    >
      新增资源
    </Button>
    {selectedAppId && treeData.length > 0 && (
      <>
        <Button
          type="default"
          icon={<SecurityScanOutlined />}
          onClick={() => setIsAssigningResources(true)}
          size="small"
        >
          分配资源
        </Button>
        {/* 新增：批量配置API权限 */}
        <Button
          type="default"
          icon={<ApiOutlined />}
          onClick={() => setIsBatchConfigApiPermissions(true)}
          size="small"
        >
          批量配置API权限
        </Button>
        {/* 新增：权限拦截测试 */}
        <Button
          type="default"
          icon={<BugOutlined />}
          onClick={() => setIsTestingPermissionInterception(true)}
          size="small"
        >
          权限拦截测试
        </Button>
      </>
    )}
  </Space>

  {/* 新增：快速操作下拉菜单 */}
  <Dropdown
    menu={{
      items: [
        {
          type: 'divider',
        },
        {
          key: 'clear-cache',
          label: '清理权限缓存',
          icon: <ClearOutlined />,
          onClick: handleClearPermissionCache,
        },
        {
          key: 'sync-permissions',
          label: '同步权限数据',
          icon: <SyncOutlined />,
          onClick: handleSyncPermissions,
        },
      ],
    }}
    trigger={['click']}
  >
    <Button size="small">
      更多操作 <DownOutlined />
    </Button>
  </Dropdown>
</div>
```

##### 5. 新增弹窗组件

**API权限绑定弹窗**：

```typescript
// API权限绑定配置弹窗
const ApiPermissionBindingModal: React.FC<{
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  resourceId: number;
  editingBinding?: any;
}> = ({ visible, onCancel, onOk, resourceId, editingBinding }) => {
  const [form] = Form.useForm();
  const [availableApis, setAvailableApis] = useState([]);
  const [availablePermissions, setAvailablePermissions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      loadAvailableApis();
      loadAvailablePermissions();
      if (editingBinding) {
        form.setFieldsValue(editingBinding);
      }
    }
  }, [visible, editingBinding]);

  const loadAvailableApis = async () => {
    try {
      setLoading(true);
      const response = await getAvailableAPIResources(resourceId);
      if (response.code === 0) {
        setAvailableApis(response.data);
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailablePermissions = async () => {
    try {
      const response = await getPermissions({ page: 1, size: 1000 });
      if (response.code === 0) {
        setAvailablePermissions(response.data);
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      await onOk({
        ...values,
        resource_id: resourceId,
      });
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  return (
    <Modal
      title={editingBinding ? "编辑API权限绑定" : "新增API权限绑定"}
      open={visible}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      onOk={handleSubmit}
      width={800}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            permission_mode: 'ANY',
            status: 'active',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="选择API"
                name="api_id"
                rules={[{ required: true, message: '请选择API' }]}
              >
                <Select
                  placeholder="请选择要绑定的API"
                  showSearch
                  filterOption={(input, option) =>
                    option?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {availableApis.map((api: any) => (
                    <Option key={api.id} value={api.id}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <Tag color={api.method === 'GET' ? 'blue' : 'green'}>
                          {api.method}
                        </Tag>
                        <span>{api.name}</span>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {api.path}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="权限模式"
                name="permission_mode"
                rules={[{ required: true }]}
              >
                <Select>
                  <Option value="ANY">需要任一权限</Option>
                  <Option value="ALL">需要全部权限</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="所需权限"
            name="required_permissions"
            rules={[{ required: true, message: '请选择至少一个权限' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择API访问所需的权限"
              showSearch
              filterOption={(input, option) =>
                option?.children?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {availablePermissions.map((permission: any) => (
                <Option key={permission.name} value={permission.name}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <KeyOutlined style={{ color: '#722ed1' }} />
                    <span>{permission.display_name}</span>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {permission.name}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="状态"
                name="status"
                rules={[{ required: true }]}
              >
                <Select>
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="优先级"
                name="priority"
                rules={[{ type: 'number', min: 1, max: 100 }]}
              >
                <InputNumber
                  min={1}
                  max={100}
                  placeholder="1-100，数字越大优先级越高"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="描述"
            name="description"
          >
            <TextArea
              rows={3}
              placeholder="请输入权限绑定的描述信息"
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};
```

##### 6. 权限拦截测试工具

**测试工具弹窗**：

```typescript
// 权限拦截测试工具
const PermissionInterceptionTestModal: React.FC<{
  visible: boolean;
  onCancel: () => void;
}> = ({ visible, onCancel }) => {
  const [form] = Form.useForm();
  const [testResult, setTestResult] = useState<any>(null);
  const [testing, setTesting] = useState(false);

  const handleTest = async () => {
    try {
      setTesting(true);
      const values = await form.validateFields();

      // 调用权限拦截测试API
      const response = await testPermissionInterception({
        api_path: values.api_path,
        method: values.method,
        user_id: values.user_id,
        page_resource_id: values.page_resource_id,
        headers: values.headers ? JSON.parse(values.headers) : {},
      });

      setTestResult(response.data);
    } catch (error) {
      showAPIError(error);
    } finally {
      setTesting(false);
    }
  };

  return (
    <Modal
      title="权限拦截测试工具"
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
        <Button key="test" type="primary" onClick={handleTest} loading={testing}>
          执行测试
        </Button>,
      ]}
    >
      <Row gutter={16}>
        <Col span={12}>
          <Card title="测试参数" size="small">
            <Form form={form} layout="vertical">
              <Form.Item
                label="API路径"
                name="api_path"
                rules={[{ required: true, message: '请输入API路径' }]}
              >
                <Input placeholder="/api/user/list" />
              </Form.Item>

              <Form.Item
                label="HTTP方法"
                name="method"
                rules={[{ required: true }]}
              >
                <Select>
                  <Option value="GET">GET</Option>
                  <Option value="POST">POST</Option>
                  <Option value="PUT">PUT</Option>
                  <Option value="DELETE">DELETE</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="测试用户ID"
                name="user_id"
                rules={[{ required: true, type: 'number' }]}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                label="页面资源ID"
                name="page_resource_id"
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                label="请求头（JSON格式）"
                name="headers"
              >
                <TextArea
                  rows={4}
                  placeholder='{"X-Page-Resource-ID": "123"}'
                />
              </Form.Item>
            </Form>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="测试结果" size="small">
            {testResult ? (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Badge
                    status={testResult.access_granted ? 'success' : 'error'}
                    text={testResult.access_granted ? '访问允许' : '访问拒绝'}
                  />
                </div>

                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="匹配的API资源">
                    {testResult.matched_api_resource?.name || '未匹配'}
                  </Descriptions.Item>
                  <Descriptions.Item label="页面上下文">
                    {testResult.page_context?.page_path || '无'}
                  </Descriptions.Item>
                  <Descriptions.Item label="权限检查模式">
                    {testResult.permission_check_mode}
                  </Descriptions.Item>
                  <Descriptions.Item label="所需权限">
                    <Space wrap>
                      {testResult.required_permissions?.map((perm: string) => (
                        <Tag key={perm}>{perm}</Tag>
                      ))}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="用户权限">
                    <Space wrap>
                      {testResult.user_permissions?.map((perm: string) => (
                        <Tag key={perm} color="blue">{perm}</Tag>
                      ))}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="检查耗时">
                    {testResult.check_duration_ms}ms
                  </Descriptions.Item>
                </Descriptions>

                {testResult.error_message && (
                  <Alert
                    type="error"
                    message="错误信息"
                    description={testResult.error_message}
                    style={{ marginTop: 16 }}
                  />
                )}
              </div>
            ) : (
              <Empty description="请执行测试查看结果" />
            )}
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};
```

#### 7.6.3 现有服务扩展

**扩展现有的资源管理服务**：

```typescript
// 扩展现有的resource.ts服务
import { apiService } from '../utils/request';
import { API_ENDPOINTS } from '../utils/request';
import { ApiResponse } from "../types";

// 新增接口类型定义
export interface ApiPermissionBinding {
  id: number;
  resource_id: number;
  api_id: number;
  api_name: string;
  api_path: string;
  method: string;
  required_permissions: string[];
  permission_mode: 'ANY' | 'ALL';
  priority: number;
  status: 'active' | 'inactive';
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateApiPermissionBindingRequest {
  resource_id: number;
  api_id: number;
  required_permissions: string[];
  permission_mode: 'ANY' | 'ALL';
  priority?: number;
  status?: 'active' | 'inactive';
  description?: string;
}

export interface PermissionInterceptionTestRequest {
  api_path: string;
  method: string;
  user_id: number;
  page_resource_id?: number;
  headers?: Record<string, string>;
}

export interface PermissionInterceptionTestResult {
  access_granted: boolean;
  matched_api_resource?: any;
  page_context?: any;
  permission_check_mode: string;
  required_permissions: string[];
  user_permissions: string[];
  check_duration_ms: number;
  error_message?: string;
}

// 新增API端点
const ENHANCED_API_ENDPOINTS = {
  ...API_ENDPOINTS,
  RESOURCE: {
    ...API_ENDPOINTS.RESOURCE,
    // API权限绑定相关
    API_PERMISSION_BINDINGS: '/api/user/resource/api-permission-bindings',
    CREATE_API_PERMISSION_BINDING: '/api/user/resource/api-permission-binding/create',
    UPDATE_API_PERMISSION_BINDING: '/api/user/resource/api-permission-binding/update',
    DELETE_API_PERMISSION_BINDING: '/api/user/resource/api-permission-binding/delete',

    // 批量操作
    BATCH_CONFIG_API_PERMISSIONS: '/api/user/resource/batch-config-api-permissions',
    APPLY_PERMISSION_TEMPLATE: '/api/user/resource/apply-permission-template',

    // 测试和监控
    TEST_PERMISSION_INTERCEPTION: '/api/user/resource/test-permission-interception',
    CLEAR_PERMISSION_CACHE: '/api/user/resource/clear-permission-cache',
    SYNC_PERMISSIONS: '/api/user/resource/sync-permissions',

    // 配置管理
    GET_GLOBAL_RULES: '/api/user/resource/global-rules',
    SAVE_GLOBAL_RULES: '/api/user/resource/global-rules/save',
    GET_CACHE_CONFIG: '/api/user/resource/cache-config',
    SAVE_CACHE_CONFIG: '/api/user/resource/cache-config/save',
  }
};

// 获取资源的API权限绑定列表
export async function getApiPermissionBindings(resourceId: number): Promise<ApiResponse<ApiPermissionBinding[]>> {
  return await apiService.post<ApiPermissionBinding[]>(
    ENHANCED_API_ENDPOINTS.RESOURCE.API_PERMISSION_BINDINGS,
    { resource_id: resourceId }
  );
}

// 创建API权限绑定
export async function createApiPermissionBinding(data: CreateApiPermissionBindingRequest): Promise<ApiResponse<ApiPermissionBinding>> {
  return await apiService.post<ApiPermissionBinding>(
    ENHANCED_API_ENDPOINTS.RESOURCE.CREATE_API_PERMISSION_BINDING,
    data
  );
}

// 更新API权限绑定
export async function updateApiPermissionBinding(id: number, data: Partial<CreateApiPermissionBindingRequest>): Promise<ApiResponse<null>> {
  return await apiService.post<null>(
    ENHANCED_API_ENDPOINTS.RESOURCE.UPDATE_API_PERMISSION_BINDING,
    { id, ...data }
  );
}

// 删除API权限绑定
export async function deleteApiPermissionBinding(id: number): Promise<ApiResponse<null>> {
  return await apiService.post<null>(
    ENHANCED_API_ENDPOINTS.RESOURCE.DELETE_API_PERMISSION_BINDING,
    { id }
  );
}

// 批量配置API权限
export async function batchConfigApiPermissions(data: {
  resource_ids: number[];
  api_permission_configs: CreateApiPermissionBindingRequest[];
}): Promise<ApiResponse<{ success_count: number; failed_count: number; }>> {
  return await apiService.post(
    ENHANCED_API_ENDPOINTS.RESOURCE.BATCH_CONFIG_API_PERMISSIONS,
    data
  );
}

// 应用权限模板
export async function applyPermissionTemplate(data: {
  resource_ids: number[];
  template_id: number;
}): Promise<ApiResponse<{ applied_count: number; }>> {
  return await apiService.post(
    ENHANCED_API_ENDPOINTS.RESOURCE.APPLY_PERMISSION_TEMPLATE,
    data
  );
}

// 测试权限拦截
export async function testPermissionInterception(data: PermissionInterceptionTestRequest): Promise<ApiResponse<PermissionInterceptionTestResult>> {
  return await apiService.post<PermissionInterceptionTestResult>(
    ENHANCED_API_ENDPOINTS.RESOURCE.TEST_PERMISSION_INTERCEPTION,
    data
  );
}

// 清理权限缓存
export async function clearPermissionCache(): Promise<ApiResponse<{ cleared_keys: number; }>> {
  return await apiService.post(
    ENHANCED_API_ENDPOINTS.RESOURCE.CLEAR_PERMISSION_CACHE,
    {}
  );
}

// 同步权限数据
export async function syncPermissions(): Promise<ApiResponse<{ synced_count: number; }>> {
  return await apiService.post(
    ENHANCED_API_ENDPOINTS.RESOURCE.SYNC_PERMISSIONS,
    {}
  );
}

// 获取全局规则配置
export async function getGlobalRules(): Promise<ApiResponse<any>> {
  return await apiService.post(
    ENHANCED_API_ENDPOINTS.RESOURCE.GET_GLOBAL_RULES,
    {}
  );
}

// 保存全局规则配置
export async function saveGlobalRules(data: any): Promise<ApiResponse<null>> {
  return await apiService.post<null>(
    ENHANCED_API_ENDPOINTS.RESOURCE.SAVE_GLOBAL_RULES,
    data
  );
}

// 获取缓存配置
export async function getCacheConfig(): Promise<ApiResponse<any>> {
  return await apiService.post(
    ENHANCED_API_ENDPOINTS.RESOURCE.GET_CACHE_CONFIG,
    {}
  );
}

// 保存缓存配置
export async function saveCacheConfig(data: any): Promise<ApiResponse<null>> {
  return await apiService.post<null>(
    ENHANCED_API_ENDPOINTS.RESOURCE.SAVE_CACHE_CONFIG,
    data
  );
}
```

#### 7.6.4 菜单配置扩展

**扩展现有的菜单配置**：

```typescript
// 在现有的menuConfigs中扩展
export const enhancedMenuConfigs: MenuConfig[] = [
  // ... 现有菜单配置
  {
    key: 'permission',
    label: '权限管理',
    path: '/permission',
  },
  {
    key: 'permission-group',
    label: '权限组管理',
    path: '/permission-group',
  },
  {
    key: 'resource',
    label: '资源管理',
    path: '/resource',
    // 扩展权限配置
    permissions: [
      'resource:read',
      'resource:create',
      'resource:update',
      'resource:delete',
      // 新增：HTTP接口权限拦截相关权限
      'resource:api-permission-binding:read',
      'resource:api-permission-binding:create',
      'resource:api-permission-binding:update',
      'resource:api-permission-binding:delete',
      'resource:permission-rules:read',
      'resource:permission-rules:update',
      'resource:permission-test:execute',
      'resource:permission-cache:manage',
    ],
  },
  // 新增：权限拦截监控页面
  {
    key: 'permission-monitoring',
    label: '权限拦截监控',
    path: '/permission-monitoring',
    permissions: [
      'permission-monitoring:read',
      'permission-monitoring:export',
    ],
  },
];
```

#### 7.6.5 权限拦截监控页面

**新增独立的监控页面**：

```typescript
// PermissionMonitoringPage.tsx
import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Select,
  Button,
  Space,
  Alert,
  Progress,
  Tag,
  Tooltip,
} from 'antd';
import {
  SecurityScanOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExportOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Line, Pie } from '@ant-design/plots';

const PermissionMonitoringPage: React.FC = () => {
  const [dateRange, setDateRange] = useState<any>([]);
  const [selectedApp, setSelectedApp] = useState<string>('all');
  const [monitoringData, setMonitoringData] = useState<any>({});
  const [loading, setLoading] = useState(false);

  // 权限拦截统计卡片
  const renderStatisticsCards = () => (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总请求数"
            value={monitoringData.total_requests || 0}
            prefix={<SecurityScanOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="通过请求"
            value={monitoringData.allowed_requests || 0}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="拒绝请求"
            value={monitoringData.denied_requests || 0}
            prefix={<CloseCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="平均响应时间"
            value={monitoringData.avg_response_time || 0}
            suffix="ms"
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: '#722ed1' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 权限拦截趋势图
  const renderTrendChart = () => {
    const config = {
      data: monitoringData.trend_data || [],
      xField: 'time',
      yField: 'count',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000,
        },
      },
    };

    return (
      <Card title="权限拦截趋势" style={{ marginBottom: 24 }}>
        <Line {...config} />
      </Card>
    );
  };

  // 权限拦截详情表格
  const renderInterceptionTable = () => {
    const columns = [
      {
        title: '时间',
        dataIndex: 'timestamp',
        render: (text: string) => new Date(text).toLocaleString(),
      },
      {
        title: 'API路径',
        dataIndex: 'api_path',
        render: (text: string, record: any) => (
          <div>
            <Tag color={record.method === 'GET' ? 'blue' : 'green'}>
              {record.method}
            </Tag>
            <span>{text}</span>
          </div>
        ),
      },
      {
        title: '用户',
        dataIndex: 'user_name',
      },
      {
        title: '页面',
        dataIndex: 'page_path',
      },
      {
        title: '结果',
        dataIndex: 'access_granted',
        render: (granted: boolean) => (
          <Tag color={granted ? 'success' : 'error'}>
            {granted ? '允许' : '拒绝'}
          </Tag>
        ),
      },
      {
        title: '耗时',
        dataIndex: 'duration_ms',
        render: (ms: number) => `${ms}ms`,
      },
      {
        title: '原因',
        dataIndex: 'reason',
        render: (reason: string) => (
          <Tooltip title={reason}>
            <span>{reason?.substring(0, 20)}...</span>
          </Tooltip>
        ),
      },
    ];

    return (
      <Card
        title="权限拦截详情"
        extra={
          <Button
            icon={<ExportOutlined />}
            onClick={() => handleExportData()}
          >
            导出数据
          </Button>
        }
      >
        <Table
          dataSource={monitoringData.interception_logs || []}
          columns={columns}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 24 }}>
        <Space>
          <DatePicker.RangePicker
            value={dateRange}
            onChange={setDateRange}
            placeholder={['开始时间', '结束时间']}
          />
          <Select
            value={selectedApp}
            onChange={setSelectedApp}
            style={{ width: 200 }}
            placeholder="选择应用"
          >
            <Select.Option value="all">全部应用</Select.Option>
            <Select.Option value="user-system">用户系统</Select.Option>
            <Select.Option value="email-system">邮件系统</Select.Option>
          </Select>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={() => loadMonitoringData()}
          >
            刷新数据
          </Button>
        </Space>
      </Card>

      {/* 统计卡片 */}
      {renderStatisticsCards()}

      {/* 缓存状态 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="缓存状态">
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>内存使用率</span>
                <span>{monitoringData.cache_memory_usage || 0}%</span>
              </div>
              <Progress
                percent={monitoringData.cache_memory_usage || 0}
                status={monitoringData.cache_memory_usage > 80 ? 'exception' : 'active'}
              />
            </div>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>缓存命中率</span>
                <span>{monitoringData.cache_hit_rate || 0}%</span>
              </div>
              <Progress
                percent={monitoringData.cache_hit_rate || 0}
                status={monitoringData.cache_hit_rate < 70 ? 'exception' : 'active'}
              />
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="性能指标">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="权限检查QPS"
                  value={monitoringData.permission_check_qps || 0}
                  suffix="/s"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="P99响应时间"
                  value={monitoringData.p99_response_time || 0}
                  suffix="ms"
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 趋势图 */}
      {renderTrendChart()}

      {/* 详情表格 */}
      {renderInterceptionTable()}

      {/* 告警信息 */}
      {monitoringData.alerts && monitoringData.alerts.length > 0 && (
        <Alert
          type="warning"
          message="系统告警"
          description={
            <ul>
              {monitoringData.alerts.map((alert: any, index: number) => (
                <li key={index}>{alert.message}</li>
              ))}
            </ul>
          }
          style={{ marginTop: 24 }}
        />
      )}
    </div>
  );
};

export default PermissionMonitoringPage;
```
```

2. **ResourceDetail 组件扩展**  
   - 添加API绑定管理标签页
   - 权限配置可视化编辑器
   - 权限冲突检测和提示

#### 7.6.2 新增专用组件

```typescript
// API绑定管理组件
export interface APIBindingManagerProps {
  pageResourceId: number
  onBindingChange: (bindings: APIBinding[]) => void
}

// 权限配置编辑器
export interface PermissionConfigEditorProps {
  apiResourceId: number
  bindings: APIBinding[]
  onConfigChange: (configs: PermissionConfig[]) => void
}

// 权限模板管理器
export interface PermissionTemplateManagerProps {
  onTemplateApply: (templateId: number, bindingIds: number[]) => void
}
```

## 8. 错误处理策略

### 8.1 增强的错误分类

```go
type EnhancedPermissionError struct {
    Code         string            `json:"code"`
    Message      string            `json:"message"`
    Details      string            `json:"details,omitempty"`
    ResourceInfo *ResourceContext  `json:"resource_info,omitempty"`
    PageContext  *PageContext      `json:"page_context,omitempty"`
}

type ResourceContext struct {
    ResourceID   int64  `json:"resource_id"`
    ResourceType string `json:"resource_type"`
    Path         string `json:"path"`
    Method       string `json:"method"`
}

const (
    // 资源相关错误
    ErrAPIResourceNotFound     = "API_RESOURCE_NOT_FOUND"        // 404
    ErrPageResourceNotFound    = "PAGE_RESOURCE_NOT_FOUND"       // 404
    ErrResourceRelationNotFound = "RESOURCE_RELATION_NOT_FOUND"   // 404
    
    // 权限相关错误
    ErrInsufficientPermissions = "INSUFFICIENT_PERMISSIONS"       // 403
    ErrContextualPermDenied    = "CONTEXTUAL_PERMISSION_DENIED"   // 403
    ErrPageAccessDenied        = "PAGE_ACCESS_DENIED"            // 403
    
    // 认证相关错误
    ErrAuthRequired           = "AUTHENTICATION_REQUIRED"         // 401
    ErrPageContextRequired    = "PAGE_CONTEXT_REQUIRED"          // 401
    
    // 配置相关错误
    ErrPermissionConfigInvalid = "PERMISSION_CONFIG_INVALID"     // 400
    ErrBindingConfigConflict   = "BINDING_CONFIG_CONFLICT"       // 400
    
    // 系统错误
    ErrInternalError          = "INTERNAL_ERROR"                 // 500
    ErrCacheError            = "CACHE_ERROR"                     // 500
)
```

### 8.2 增强的回退策略

```go
type EnhancedFallbackConfig struct {
    // 资源未找到的回退策略
    APIResourceNotFoundMode  FallbackMode `yaml:"api_resource_not_found_mode"`
    PageResourceNotFoundMode FallbackMode `yaml:"page_resource_not_found_mode"`
    
    // 页面上下文缺失的回退策略
    PageContextMissingMode   FallbackMode `yaml:"page_context_missing_mode"`
    
    // 权限查询失败的回退策略
    PermissionQueryFailMode  FallbackMode `yaml:"permission_query_fail_mode"`
    
    // 缓存失败的回退策略
    CacheFailureMode        FallbackMode `yaml:"cache_failure_mode"`
}

func (c *EnhancedPermissionInterceptor) handleAPIResourceNotFound(ctx *gin.Context, config *EnhancedFallbackConfig) {
    switch config.APIResourceNotFoundMode {
    case FallbackModeAllow:
        // 允许访问，记录日志
        logger.Warn(ctx, "API resource not found, allowing access due to fallback config")
        ctx.Next()
    case FallbackModeDeny:
        // 拒绝访问
        commonResponse.NotFound(ctx, "API resource not found")
    case FallbackModeError:
        // 返回系统错误
        commonResponse.InternalError(ctx, "Resource resolution failed")
    }
}

func (c *EnhancedPermissionInterceptor) handlePageContextMissing(ctx *gin.Context, config *EnhancedFallbackConfig) {
    switch config.PageContextMissingMode {
    case FallbackModeAllow:
        // 使用基础权限检查
        logger.Info(ctx, "Page context missing, using basic permission check")
        return c.basicPermissionCheck(ctx)
    case FallbackModeDeny:
        // 要求页面上下文
        commonResponse.BadRequest(ctx, "Page context required")
    case FallbackModeError:
        // 返回配置错误
        commonResponse.InternalError(ctx, "Page context resolution failed")
    }
}
```

### 8.3 权限冲突检测

```go
type PermissionConflictDetector struct {
    relationRepo repository.ResourceRelationRepository
    logger       logiface.Logger
}

type ConflictReport struct {
    HasConflicts bool                `json:"has_conflicts"`
    Conflicts    []PermissionConflict `json:"conflicts"`
    Suggestions  []string            `json:"suggestions"`
}

type PermissionConflict struct {
    Type        string `json:"type"`         // scope_conflict, requirement_conflict, inheritance_conflict
    Description string `json:"description"`
    Resources   []struct {
        PageName       string `json:"page_name"`
        PermissionCode string `json:"permission_code"`
        PermissionScope string `json:"permission_scope"`
    } `json:"resources"`
    Severity    string `json:"severity"`     // low, medium, high, critical
}

func (pcd *PermissionConflictDetector) DetectConflicts(ctx context.Context, apiResourceID int64) (*ConflictReport, error) {
    relations, err := pcd.relationRepo.GetByTargetResourceID(ctx, apiResourceID)
    if err != nil {
        return nil, err
    }
    
    report := &ConflictReport{
        Conflicts: make([]PermissionConflict, 0),
    }
    
    // 检测权限范围冲突
    report.Conflicts = append(report.Conflicts, pcd.detectScopeConflicts(relations)...)
    
    // 检测必需权限冲突
    report.Conflicts = append(report.Conflicts, pcd.detectRequirementConflicts(relations)...)
    
    // 检测继承权限冲突
    report.Conflicts = append(report.Conflicts, pcd.detectInheritanceConflicts(relations)...)
    
    report.HasConflicts = len(report.Conflicts) > 0
    
    if report.HasConflicts {
        report.Suggestions = pcd.generateSuggestions(report.Conflicts)
    }
    
    return report, nil
}
```

## 9. 监控与运维

### 9.1 增强的性能指标

```go
type EnhancedPermissionMetrics struct {
    // 基础指标
    RequestTotal          int64   `json:"request_total"`
    SuccessRate          float64 `json:"success_rate"`
    AvgResponseTime      float64 `json:"avg_response_time_ms"`
    
    // 缓存指标
    CacheHitRate         float64 `json:"cache_hit_rate"`
    CacheMemoryUsage     int64   `json:"cache_memory_usage_bytes"`
    
    // 资源匹配指标
    APIResourceMatchRate    float64 `json:"api_resource_match_rate"`
    PageContextDetectRate   float64 `json:"page_context_detect_rate"`
    ContextualPermHitRate   float64 `json:"contextual_perm_hit_rate"`
    
    // 权限检查指标
    DirectPermissionRate    float64 `json:"direct_permission_rate"`
    InheritedPermissionRate float64 `json:"inherited_permission_rate"`
    ConflictDetectionRate   float64 `json:"conflict_detection_rate"`
    
    // 错误指标
    ResourceNotFoundRate    float64 `json:"resource_not_found_rate"`
    PermissionDeniedRate    float64 `json:"permission_denied_rate"`
    ConfigConflictRate      float64 `json:"config_conflict_rate"`
    
    // 性能分布
    P50ResponseTime      float64 `json:"p50_response_time_ms"`
    P95ResponseTime      float64 `json:"p95_response_time_ms"`
    P99ResponseTime      float64 `json:"p99_response_time_ms"`
}
```

### 9.2 监控告警配置

```yaml
monitoring:
  metrics:
    collection_interval: 30s
    export_endpoint: /metrics
    
  alerts:
    - name: high_permission_denied_rate
      condition: permission_denied_rate > 0.1
      severity: warning
      message: "权限拒绝率过高，可能存在配置问题"
      
    - name: low_cache_hit_rate
      condition: cache_hit_rate < 0.8
      severity: warning  
      message: "缓存命中率过低，需要优化缓存策略"
      
    - name: high_conflict_detection_rate
      condition: conflict_detection_rate > 0.05
      severity: critical
      message: "权限配置冲突频繁，需要立即处理"
      
    - name: api_resource_match_failure
      condition: api_resource_match_rate < 0.95
      severity: error
      message: "API资源匹配失败率过高"

  dashboards:
    - name: "权限系统总览"
      panels:
        - requests_per_second
        - success_rate
        - response_time_distribution
        - cache_hit_rate
        
    - name: "权限配置监控"
      panels:
        - contextual_permission_usage
        - binding_configuration_status
        - conflict_detection_trends
        - permission_template_usage
```

### 9.3 运维建议

#### 9.3.1 部署配置

```yaml
# 生产环境配置示例
enhanced_permission:
  enable: true
  
  # 缓存配置
  cache:
    max_memory_mb: 200
    api_resource_ttl: 7200s      # 2小时
    resource_relation_ttl: 3600s  # 1小时
    contextual_perm_ttl: 1800s   # 30分钟
    user_role_ttl: 1800s         # 30分钟
    enable_cascade_invalid: true
    enable_smart_preload: true
  
  # 回退配置
  fallback:
    api_resource_not_found_mode: "DENY"
    page_context_missing_mode: "ALLOW"     # 允许降级到基础权限检查
    permission_query_fail_mode: "ERROR"
    cache_failure_mode: "ALLOW"
  
  # 页面上下文配置
  page_context:
    header_name: "X-Page-Resource-ID"
    enable_referer_parsing: true
    enable_query_param: true
    required_for_apis: []  # 可配置特定API必须要求页面上下文
  
  # 性能配置
  performance:
    enable_concurrent_checks: true
    max_concurrent_checks: 100
    permission_check_timeout: 500ms
    cache_warmup_on_startup: true
```

#### 9.3.2 数据库优化

```sql
-- 推荐的数据库索引
-- API资源快速查找
CREATE INDEX idx_resource_api_lookup ON resource(tenant_id, internal_app_id, path, api_method, resource_type);

-- 资源关系查询优化
CREATE INDEX idx_resource_relations_api_lookup ON resource_relations(target_resource_id, tenant_id, internal_app_id);
CREATE INDEX idx_resource_relations_page_lookup ON resource_relations(source_resource_id, tenant_id, internal_app_id);

-- 权限配置查询优化
CREATE INDEX idx_resource_relations_permission_lookup ON resource_relations(permission_code, permission_scope);

-- 组合索引用于复杂查询
CREATE INDEX idx_resource_relations_complex ON resource_relations(tenant_id, internal_app_id, source_resource_id, target_resource_id, permission_scope);
```

## 10. 总结

### 10.1 设计方案总结

本增强设计方案基于现有的 users 模块资源管理体系，实现了以下核心功能：

**主要特性**：
- **API与页面绑定管理**：通过 `resource_relations` 表实现API与页面的灵活绑定
- **权限差异化配置**：同一个API在不同页面下可配置不同权限要求
- **管理界面集成**：完全集成现有资源管理功能，避免管理分散
- **高性能缓存**：四层缓存架构，智能预加载和级联失效
- **完善的监控**：全方位性能监控和运维支持

**技术优势**：
- **向后兼容**：完全兼容现有资源管理功能
- **性能优化**：相比基础版本节省50%内存占用
- **灵活配置**：支持多种回退策略和运维配置
- **易于扩展**：清晰的架构设计便于后续功能扩展

**管理便利性**：
- **统一管理界面**：在现有资源树中直接管理API绑定
- **可视化配置**：直观的权限配置和冲突检测
- **模板化管理**：权限模板快速批量应用
- **完善的用户体验**：符合现有操作习惯的界面设计

### 10.2 实施建议

1. **阶段性实施**：
   - 第一阶段：扩展 `resource_relations` 表和基础API
   - 第二阶段：实现权限拦截中间件和缓存机制  
   - 第三阶段：完善管理界面和监控系统

2. **数据迁移**：
   - 现有资源数据无需迁移
   - 可选择性地为现有API创建页面绑定关系
   - 提供数据导入工具批量创建绑定关系

3. **性能测试**：
   - 在生产环境部署前进行压力测试
   - 验证缓存策略和性能指标
   - 制定容量规划和扩容策略

该方案能够在保证系统安全性的前提下，提供高性能、易管理的HTTP接口权限拦截服务，完美融合现有系统架构，适用于大规模多租户环境。