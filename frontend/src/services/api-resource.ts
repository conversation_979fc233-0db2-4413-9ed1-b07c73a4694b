import {apiService, API_ENDPOINTS} from '../utils/request';
import {ApiResponse} from '../types';
import {APIResource} from './resource';

// API资源请求接口
export interface CreateAPIResourceRequest {
    name: string;
    display_name: string;
    description?: string;
    permission_id?: number;
    service_name: string;
    api_method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    path: string;
    request_type?: string;
    response_type?: string;
    content_type?: string;
    is_public?: boolean;
    status?: 'active' | 'inactive';
}

export interface UpdateAPIResourceRequest {
    id: number;
    name: string;
    display_name: string;
    description?: string;
    permission_id?: number;
    service_name: string;
    api_method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    path: string;
    request_type?: string;
    response_type?: string;
    content_type?: string;
    is_public?: boolean;
    status?: 'active' | 'inactive';
}

export interface ListAPIResourcesRequest {
    internal_app_id?: number;
    service_name?: string;
    api_method?: string;
    path?: string;
    status?: string;
    is_public?: boolean;
    permission_id?: number;
    keyword?: string;
    page?: number;
    page_size?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
}

export interface APIResourceListResponse {
    items: APIResource[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
}

export interface AssignPermissionToAPIResourceRequest {
    api_resource_id: number;
    permission_id: number;
}

export interface BatchCreateAPIResourcesRequest {
    api_resources: CreateAPIResourceRequest[];
}

export interface BatchDeleteAPIResourcesRequest {
    ids: number[];
}

export interface UpdateAPIResourceStatusRequest {
    id: number;
    status: 'active' | 'inactive';
}

// 关联关系相关接口
export interface CreateResourceAPIRelationRequest {
    resource_id: number;
    api_resource_id: number;
}

export interface BatchAssignAPIsToResourceRequest {
    resource_id: number;
    api_resource_ids: number[];
}

export interface ResourceWithAPIs {
    resource: any; // Resource interface from resource.ts
    api_resources: APIResource[];
    relations: any[];
}

export interface APIResourceWithResources {
    api_resource: APIResource;
    resources: any[]; // Resource interface from resource.ts
    relations: any[];
}

export interface ListResourcesWithAPIsRequest {
    internal_app_id?: number;
    resource_id?: number;
    resource_type?: string;
    page?: number;
    page_size?: number;
}

export interface ListAPIsWithResourcesRequest {
    internal_app_id?: number;
    api_resource_id?: number;
    service_name?: string;
    api_method?: string;
    page?: number;
    page_size?: number;
}

export interface ResourceWithAPIsListResponse {
    items: ResourceWithAPIs[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
}

export interface APIResourceWithResourcesListResponse {
    items: APIResourceWithResources[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
}

// API资源管理服务函数
export async function createAPIResource(data: CreateAPIResourceRequest): Promise<ApiResponse<APIResource>> {
    return await apiService.post<APIResource>(API_ENDPOINTS.API_RESOURCE.CREATE, data);
}

export async function updateAPIResource(data: UpdateAPIResourceRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.API_RESOURCE.UPDATE, data);
}

export async function deleteAPIResource(id: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.API_RESOURCE.DELETE, { id });
}

export async function getAPIResource(id: number): Promise<ApiResponse<APIResource>> {
    return await apiService.get<APIResource>(API_ENDPOINTS.API_RESOURCE.GET, { params: { id } });
}

export async function listAPIResources(params?: ListAPIResourcesRequest): Promise<ApiResponse<APIResourceListResponse>> {
    return await apiService.post<APIResourceListResponse>(API_ENDPOINTS.API_RESOURCE.LIST, params || {});
}

export async function assignPermissionToAPIResource(data: AssignPermissionToAPIResourceRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.API_RESOURCE.ASSIGN_PERMISSION, data);
}

export async function batchCreateAPIResources(data: BatchCreateAPIResourcesRequest): Promise<ApiResponse<APIResource[]>> {
    return await apiService.post<APIResource[]>(API_ENDPOINTS.API_RESOURCE.BATCH_CREATE, data);
}

export async function batchDeleteAPIResources(data: BatchDeleteAPIResourcesRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.API_RESOURCE.BATCH_DELETE, data);
}

export async function updateAPIResourceStatus(data: UpdateAPIResourceStatusRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.API_RESOURCE.UPDATE_STATUS, data);
}

// 关联关系管理服务函数
export async function createResourceAPIRelation(data: CreateResourceAPIRelationRequest): Promise<ApiResponse<any>> {
    return await apiService.post<any>(API_ENDPOINTS.RESOURCE_API_RELATION.CREATE, data);
}

export async function deleteResourceAPIRelation(resourceId: number, apiResourceId: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.RESOURCE_API_RELATION.DELETE, { resource_id: resourceId, api_resource_id: apiResourceId });
}

export async function getResourceAPIs(resourceId: number): Promise<ApiResponse<APIResource[]>> {
    return await apiService.get<APIResource[]>(API_ENDPOINTS.RESOURCE_API_RELATION.GET_RESOURCE_APIS, { params: { resource_id: resourceId } });
}

export async function getAPIResources(apiResourceId: number): Promise<ApiResponse<any[]>> {
    return await apiService.get<any[]>(API_ENDPOINTS.RESOURCE_API_RELATION.GET_API_RESOURCES, { params: { api_resource_id: apiResourceId } });
}

export async function batchAssignAPIsToResource(data: BatchAssignAPIsToResourceRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.RESOURCE_API_RELATION.BATCH_ASSIGN_APIS, data);
}

export async function listResourcesWithAPIs(params?: ListResourcesWithAPIsRequest): Promise<ApiResponse<ResourceWithAPIsListResponse>> {
    return await apiService.post<ResourceWithAPIsListResponse>(API_ENDPOINTS.RESOURCE_API_RELATION.LIST_RESOURCES_WITH_APIS, params || {});
}

export async function listAPIsWithResources(params?: ListAPIsWithResourcesRequest): Promise<ApiResponse<APIResourceWithResourcesListResponse>> {
    return await apiService.post<APIResourceWithResourcesListResponse>(API_ENDPOINTS.RESOURCE_API_RELATION.LIST_APIS_WITH_RESOURCES, params || {});
}