import {apiService} from '../utils/request';
import {API_ENDPOINTS} from '../utils/request';
import {ApiResponse} from '../types';

export interface Resource {
    id: number;
    tenant_id: number;
    internal_app_id: number;
    name: string;
    display_name: string;
    description?: string;
    resource_type: string;
    permission_id?: number; // 关联的权限ID
    parent_id?: number;
    path?: string;
    icon?: string;
    sort_order: number;
    is_system: boolean;
    is_universal: boolean;
    status: string;
    depth?: number;
    has_children?: boolean;
    children_count?: number;
    created_at: string;
    updated_at: string;
    permissions?: Permission[];
    api_resources?: APIResource[]; // 关联的API资源
    is_public: boolean;
    public_level: 'none' | 'anonymous' | 'authenticated' | 'conditional';
    assignable: boolean;
    assignability_status: string;
}

export interface Permission {
    id: number;
    name: string;
    display_name: string;
    description?: string;
    status: string;
    created_at: string;
    updated_at: string;
}

export interface APIResource {
    id: number;
    tenant_id: number;
    internal_app_id: number;
    name: string;
    display_name: string;
    description?: string;
    permission_id?: number;
    service_name: string;
    api_method: string;
    path: string;
    request_type: string;
    response_type: string;
    content_type?: string;
    is_public: boolean;
    status: string;
    created_at: string;
    updated_at: string;
}

export interface ResourceTreeNode {
    id: number;
    tenant_id: number;
    name: string;
    display_name: string;
    description?: string;
    resource_type: string;
    parent_id?: number;
    path?: string;
    icon?: string;
    sort_order: number;
    is_system: boolean;
    is_universal: boolean;
    status: string;
    depth: number;
    has_children: boolean;
    children_count: number;
    children?: ResourceTreeNode[];
}

export interface CreateResourceRequest {
    name: string;
    display_name: string;
    description?: string;
    resource_type: string;
    permission_id?: number; // 关联的权限ID
    parent_id?: number;
    path?: string;
    icon?: string;
    sort_order?: number;
    is_system?: boolean;
    is_universal?: boolean;
    is_public?: boolean;
    public_level?: 'none' | 'anonymous' | 'authenticated' | 'conditional';
    assignable?: boolean;
    tenant_id?: number; // 新增：租户ID
    internal_app_id?: number; // 新增：应用内部ID
}

export interface UpdateResourceRequest {
    id: number;
    name: string;
    display_name: string;
    description?: string;
    resource_type: string;
    permission_id?: number; // 关联的权限ID
    parent_id?: number;
    path?: string;
    icon?: string;
    sort_order?: number;
    status?: string;
    is_public?: boolean;
    public_level?: 'none' | 'anonymous' | 'authenticated' | 'conditional';
    assignable?: boolean;
}

export interface ListResourcesRequest {
    page?: number;
    size?: number;
    keyword?: string;
    resource_type?: string;
    parent_id?: number;
    is_system?: boolean;
    path?: string;
    order_by?: string;
    order_dir?: string;
}


export interface ListResourceTreeRequest {
    tenant_id?: number;
    resource_type?: string;
    include_universal?: boolean;
    internal_app_id?: number;
}

export interface ResourcePermissionsRequest {
    resource_id: number;
}

export interface ResourcePermissionsResponse {
    resource: Resource;
    permissions: Permission[];
}

export interface AvailableAPIResourcesRequest {
    page_resource_id: number;
}

export interface AvailableAPIResourcesResponse {
    code: number;
    message: string;
    data: {
        available_resources: Resource[];
        assigned_resources: Resource[];
    };
    meta: {
        request_id: string;
        timestamp: number;
        timestamp_str: string;
    };
}

export interface BatchAssignAPIResourcesRequest {
    page_resource_id: number;
    api_resource_ids: number[];
}

export interface ResourceStats {
    total_resources: number;
    type_counts: Record<string, number>;
    max_depth: number;
    universal_resources: number;
    tenant_resources: number;
}

// 资源权限配置相关接口
export interface ResourcePermissionItem {
    name: string;
    code: string;
    display_name: string;
    action: string; // 操作类型：create, read, update, delete等
    scope: string;
    description?: string;
}

export interface ConfigureResourcePermissionsRequest {
    permissions: ResourcePermissionItem[];
}

export interface ConfigureResourcePermissionsResponse {
    created_permissions: Permission[];
    skipped_permissions: SkippedPermission[];
    success_count: number;
    skipped_count: number;
    total_count: number;
}

export interface SkippedPermission {
    name: string;
    code: string;
    display_name: string;
    action: string; // 操作类型
    reason: string;
}

// 资源关系相关接口
export interface ResourceRelation {
    id: number;
    tenant_id: number;
    internal_app_id: number;
    source_resource_id: number;
    target_resource_id: number;
    permission_id?: number; // 权限ID，关联permissions表
    description?: string;
    permission_code?: string; // 权限编码
    is_required?: boolean; // 是否必需权限
    inherit_parent?: boolean; // 是否继承父级权限
    priority?: number; // 权限优先级
    status?: string; // 状态
    created_at: string;
    updated_at: string;
    source_resource?: Resource;
    target_resource?: Resource;
    permission?: Permission;
}

export interface CreateResourceRelationRequest {
    tenant_id: number;
    internal_app_id: number;
    source_resource_id: number;
    target_resource_id: number;
    permission_id?: number; // 权限ID，关联permissions表
    description?: string;
    permission_code?: string;
    is_required?: boolean;
    inherit_parent?: boolean;
    priority?: number;
    status?: string;
}

export interface UpdateResourceRelationRequest {
    id: number;
    permission_id?: number; // 权限ID，关联permissions表
    description?: string;
    permission_code?: string;
    is_required?: boolean;
    inherit_parent?: boolean;
    priority?: number;
    status?: string;
}

export interface ListResourceRelationsRequest {
    tenant_id?: number;
    internal_app_id?: number;
    source_resource_id?: number;
    target_resource_id?: number;
    status?: string;
    page?: number;
    size?: number;
}

export interface ResourceRelationResponse {
    id: number;
    tenant_id: number;
    internal_app_id: number;
    source_resource_id: number;
    target_resource_id: number;
    permission_id?: number; // 权限ID，关联permissions表
    description?: string;
    permission_code?: string;
    is_required?: boolean;
    inherit_parent?: boolean;
    priority?: number;
    status?: string;
    status_display?: string;
    created_at: string;
    updated_at: string;
    source_resource?: Resource;
    target_resource?: Resource;
    permission?: Permission;
}

// 获取资源列表
export async function getResources(params?: ListResourcesRequest): Promise<ApiResponse<Resource[]>> {
    return await apiService.post<Resource[]>(API_ENDPOINTS.RESOURCE.LIST, params || {});
}

// 获取资源详情
export async function getResource(id: number): Promise<ApiResponse<Resource>> {
    return await apiService.post<Resource>(API_ENDPOINTS.RESOURCE.GET, {id});
}

// 创建资源
export async function createResource(data: CreateResourceRequest): Promise<ApiResponse<Resource>> {
    return await apiService.post<Resource>(API_ENDPOINTS.RESOURCE.CREATE, data);
}

// 更新资源
export async function updateResource(updateData: UpdateResourceRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.RESOURCE.UPDATE, updateData);
}

// 删除资源
export async function deleteResource(id: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.RESOURCE.DELETE, {id});
}

// 获取资源树
export async function getResourceTree(params?: ListResourceTreeRequest): Promise<ApiResponse<ResourceTreeNode[]>> {
    return await apiService.post<ResourceTreeNode[]>(API_ENDPOINTS.RESOURCE.TREE, params || {});
}

// 获取资源权限
export async function getResourcePermissions(resourceId: number): Promise<ApiResponse<ResourcePermissionsResponse>> {
    return await apiService.post<ResourcePermissionsResponse>(
        API_ENDPOINTS.RESOURCE.PERMISSIONS,
        {resource_id: resourceId}
    );
}

// 获取可用的API资源
export async function getAvailableAPIResources(pageResourceId: number): Promise<ApiResponse<AvailableAPIResourcesResponse>> {
    return await apiService.post<AvailableAPIResourcesResponse>(
        API_ENDPOINTS.RESOURCE.API_RESOURCES_AVAILABLE,
        {page_resource_id: pageResourceId}
    );
}

// 批量分配API资源
export async function batchAssignAPIResources(data: BatchAssignAPIResourcesRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.RESOURCE.API_RESOURCES_ASSIGN, data);
}

// 获取资源统计
export async function getResourceStats(): Promise<ApiResponse<ResourceStats>> {
    return await apiService.post<ResourceStats>(API_ENDPOINTS.RESOURCE.STATS, {});
}

// 配置资源权限
export async function configureResourcePermissions(data: ConfigureResourcePermissionsRequest): Promise<ApiResponse<ConfigureResourcePermissionsResponse>> {
    return  await apiService.post<ConfigureResourcePermissionsResponse>(
        API_ENDPOINTS.RESOURCE.PERMISSIONS_CONFIGURE,
        data
    );
}

// 将资源分配给应用
export interface AssignResourcesToAppRequest {
    resource_ids: number[];
    app_name: string;
    tenant_id?: number;
    target_internal_app_id?: number; // 新增目标应用ID参数
}

export async function assignResourcesToApp(data: AssignResourcesToAppRequest): Promise<ApiResponse<any>> {
    return await apiService.post<any>(API_ENDPOINTS.RESOURCE.ASSIGN_TO_APP, data);
} 