import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Switch,
  Tooltip,
  Badge,
  Tree,
  Empty,

} from 'antd';
import { SUCCESS } from '../../constants/errorCodes';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  TeamOutlined,
  SettingOutlined,
  UserOutlined,
  KeyOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import { getPermissions } from '../../services/permission';
import { useTheme } from '../../contexts/ThemeContext';
import { showAPIError } from '../../utils/errorHandler';
import PermissionAssigner from '../permission/components/PermissionAssigner';
import {
  getRoles, 
  createRole, 
  updateRole, 
  deleteRole, 
  assignPermissions, 
  assignRole,
  getRoleStats,
  getRolePermissions,
  type Role,
  type Permission,
  type AssignPermissionRequest,
  type AssignRoleRequest,
  type ListRoleRequest,
  type RoleStats
} from '../../services/role';
import { showError, showSuccess } from '../../utils/messageManager';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 转换权限数据为Tree组件需要的格式
const convertPermissionsToTreeData = (permissions: Permission[]): any[] => {
  return permissions.map(perm => ({
    ...perm,
    key: perm.id,
    title: perm.display_name,
  }));
};

const RolePage: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionTreeData, setPermissionTreeData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<number[]>([]);
  const [stats, setStats] = useState<RoleStats | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<ListRoleRequest>({
    page: 1,
    size: 10,
  });
  const [form] = Form.useForm();
  const [assignForm] = Form.useForm();
  const [permissionAssignerVisible, setPermissionAssignerVisible] = useState(false);
  const [permissionAssignerLoading, setPermissionAssignerLoading] = useState(false);

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [rolesRes, permissionsRes] = await Promise.all([
        getRoles(searchParams),
        getPermissions(),
      ]);
      console.log('rolesRes',rolesRes)
      
      // 检查角色数据的业务状态码
      if (rolesRes?.code === 0) {
        setRoles(rolesRes.data || []);
        setPagination(prev => ({
          ...prev,
          current: rolesRes.meta?.pagination?.page || 1,
          total: rolesRes.meta?.pagination?.total || 0,
        }));
      } else {
        console.warn('获取角色列表失败:', rolesRes?.message);
        setRoles([]);
      }
      
      // 检查权限数据的业务状态码
      if (permissionsRes?.code === 0) {
        setPermissions(permissionsRes.data || []);
        setPermissionTreeData(convertPermissionsToTreeData(permissionsRes.data || []));
      } else {
        console.warn('获取权限列表失败:', permissionsRes?.message);
        setPermissions([]);
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  const fetchStats = useCallback(async () => {
    try {
      const statsData = await getRoleStats();
      if (statsData.code === 0) {
        setStats(statsData.data);
      } else {
        console.warn('获取统计数据失败:', statsData.message);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  }, []);

  useEffect(() => {
    fetchData();
    fetchStats();
  }, [fetchData, fetchStats]);

  const handleAdd = () => {
    setEditingRole(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: Role) => {
    setEditingRole(record);
    form.setFieldsValue({
      name: record.name,
      code: record.name, // 使用name作为code
      display_name: record.display_name,
      description: record.description,
      status: record.status,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个角色吗？删除后无法恢复。',
      onOk: async () => {
        try {
          const response = await deleteRole(id);
          if (response.code === SUCCESS) {
            message.success('角色删除成功');
            fetchData();
            fetchStats();
                  } else {
          showError(response.message || '角色删除失败', 'role-delete-error');
        }
      } catch (error) {
        console.error('Failed to delete role:', error);
        showError('角色删除失败', 'role-delete-error');
      }
      },
    });
  };

  const handleAssignPermissions = async (record: Role) => {
    setSelectedRoleId(record.id);
    try {
      const permissionsRes = await getRolePermissions(record.id);
      if (permissionsRes.code === 0) {
        setSelectedPermissions(permissionsRes.data.map((p: any) => p.id));
        setPermissionAssignerVisible(true);
      } else {
        console.error('Failed to get role permissions:', permissionsRes.message);
      }
    } catch (error) {
      console.error('Failed to get role permissions:', error);
    }
  };

  const handleAssignUsers = (record: Role) => {
    setSelectedRoleId(record.id);
    assignForm.resetFields();
    setAssignModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      if (editingRole) {
        const response = await updateRole({ ...editingRole, ...values });
        if (response.code === SUCCESS) {
          showSuccess('角色更新成功');
        } else {
          showError(response.message || '角色更新失败', 'role-update-error');
          return;
        }
      } else {
        const response = await createRole(values);
        if (response.code === SUCCESS) {
          showSuccess('角色创建成功');
        } else {
          showError(response.message || '角色创建失败', 'role-create-error');
          return;
        }
      }
      setModalVisible(false);
      // 直接重新获取数据，而不是依赖旧的fetchData闭包
      const rolesRes = await getRoles(searchParams);
      if (rolesRes?.code === 0) {
        setRoles(rolesRes.data || []);
        setPagination(prev => ({
          ...prev,
          current: rolesRes.meta?.pagination?.page || 1,
          total: rolesRes.meta?.pagination?.total || 0,
        }));
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  const handlePermissionSubmit = async () => {
    if (!selectedRoleId) return;
    
    try {
      const assignData: AssignPermissionRequest = {
        role_id: selectedRoleId,
        permission_ids: selectedPermissions,
      };
      const response = await assignPermissions(selectedRoleId, assignData);
      if (response.code === SUCCESS) {
        setPermissionModalVisible(false);
        fetchData();
      } else {
        showError(response.message || '权限分配失败', 'permission-assign-error');
      }
    } catch (error) {
      console.error('Failed to assign permissions:', error);
    }
  };

  const handlePermissionAssignerOk = async (selectedPermissions: number[]) => {
    if (!selectedRoleId) return;
    
    setPermissionAssignerLoading(true);
    try {
      const assignData: AssignPermissionRequest = {
        role_id: selectedRoleId,
        permission_ids: selectedPermissions,
      };
      const response = await assignPermissions(selectedRoleId, assignData);
      if (response.code === SUCCESS) {
        setPermissionAssignerVisible(false);
        fetchData();
        showSuccess('权限分配成功');
      } else {
        showError(response.message || '权限分配失败', 'permission-assign-error');
      }
    } catch (error) {
      console.error('Failed to assign permissions:', error);
      showError('权限分配失败', 'permission-assign-error');
    } finally {
      setPermissionAssignerLoading(false);
    }
  };

  const handlePermissionAssignerCancel = () => {
    setPermissionAssignerVisible(false);
  };

  const handleAssignSubmit = async (values: any) => {
    if (!selectedRoleId) return;
    
    try {
      const assignData: AssignRoleRequest = {
        role_id: selectedRoleId,
        user_ids: values.user_ids,
      };
      const response = await assignRole(selectedRoleId, assignData);
      if (response.code === SUCCESS) {
        setAssignModalVisible(false);
        fetchData();
      } else {
        showError(response.message || '用户分配失败', 'user-assign-error');
      }
    } catch (error) {
      console.error('Failed to assign users:', error);
    }
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSearchParams(prev => ({
      ...prev,
      page: pagination.current,
      size: pagination.pageSize,
    }));
  };

  const handleSearch = (value: string) => {
    setSearchParams(prev => ({
      ...prev,
      keyword: value,
      page: 1,
    }));
  };

  const handleFilter = (field: string, value: any) => {
    setSearchParams(prev => ({
      ...prev,
      [field]: value,
      page: 1,
    }));
  };

  const actionButtonStyle = {
    color: isDarkMode ? '#40a9ff' : '#1890ff',
    fontWeight: 500,
  };

  const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

  const columns = [
    {
      title: '角色名称',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text: string, record: Role) => (
        <div>
          <div style={{ fontWeight: 500, color: isDarkMode ? '#ffffff' : '#262626' }}>
            {text}
          </div>
          <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
            {record.name}
          </div>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <Text style={{ color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
          {text || '暂无描述'}
        </Text>
      ),
    },
    {
      title: '用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      width: 80,
      render: (count: number) => (
        <Badge count={count} size="small" />
      ),
    },
    {
      title: '权限数',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 80,
      render: (permissions: Permission[]) => (
        <Badge count={permissions?.length || 0} size="small" />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '系统角色',
      dataIndex: 'is_system',
      key: 'is_system',
      width: 100,
      render: (isSystem: boolean) => (
        <Tag color={isSystem ? 'blue' : 'default'}>
          {isSystem ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => date,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: Role) => (
        <Space size="small">
          <Tooltip title="编辑" color={tooltipColor}>
            <Button type="text" size="small" icon={<EditOutlined />} onClick={() => handleEdit(record)} style={actionButtonStyle} />
          </Tooltip>
          <Tooltip title="分配权限" color={tooltipColor}>
            <Button type="text" size="small" icon={<KeyOutlined />} onClick={() => handleAssignPermissions(record)} style={actionButtonStyle} />
          </Tooltip>
          <Tooltip title="分配用户" color={tooltipColor}>
            <Button type="text" size="small" icon={<KeyOutlined />} onClick={() => handleAssignUsers(record)} style={actionButtonStyle} />
          </Tooltip>
          {!record.is_system && (
            <Tooltip title="删除" color={tooltipColor}>
              <Button type="text" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDelete(record.id)} style={actionButtonStyle} />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题区域 - 改为统计展示 */}
      <div style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          {stats && (
            <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: '#1890ff15',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#1890ff',
                    fontSize: 18,
                  }}
                >
                  <TeamOutlined />
                </div>
                <div>
                  <div style={{ 
                    fontSize: 24, 
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stats.total_roles?.toLocaleString() || 0}
                  </div>
                  <div style={{ 
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    总角色数
                  </div>
                </div>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: '#52c41a15',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#52c41a',
                    fontSize: 18,
                  }}
                >
                  <SafetyCertificateOutlined />
                </div>
                <div>
                  <div style={{ 
                    fontSize: 24, 
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stats.active_roles?.toLocaleString() || 0}
                  </div>
                  <div style={{ 
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    启用角色
                  </div>
                </div>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: '#722ed115',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#722ed1',
                    fontSize: 18,
                  }}
                >
                  <SettingOutlined />
                </div>
                <div>
                  <div style={{ 
                    fontSize: 24, 
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stats.system_roles?.toLocaleString() || 0}
                  </div>
                  <div style={{ 
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    系统角色
                  </div>
                </div>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: '#fa8c1615',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#fa8c16',
                    fontSize: 18,
                  }}
                >
                  <UserOutlined />
                </div>
                <div>
                  <div style={{ 
                    fontSize: 24, 
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stats.total_users?.toLocaleString() || 0}
                  </div>
                  <div style={{ 
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    总用户数
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* 操作按钮 */}
          <Space>
            <Button icon={<ReloadOutlined />} onClick={() => { fetchData(); fetchStats(); }} style={actionButtonStyle}>
              刷新
            </Button>
            <Button icon={<ReloadOutlined />} onClick={() => {
              setSearchParams({ page: 1, size: 10 });
            }} style={actionButtonStyle}>
              重置
            </Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增角色
            </Button>
          </Space>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <Card style={{
        marginBottom: 16,
        borderRadius: 8,
        borderStyle: 'none',
        background: isDarkMode ? '#1f1f1f' : '#ffffff',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
      }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Input.Search
              placeholder="搜索角色名称或代码..."
              onSearch={handleSearch}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('status', value)}
            >
              <Option value="active">启用</Option>
              <Option value="disabled">禁用</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="系统角色"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('is_system', value)}
            >
              <Option value={true}>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </Col>
          <Col span={10}>
            <Space>
              <Button icon={<SearchOutlined />} onClick={() => handleSearch('')}>
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => {
                setSearchParams({ page: 1, size: 10 });
              }}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 角色列表 */}
      <Card style={{
        borderRadius: 8,
        borderStyle: 'none',
        background: isDarkMode ? '#1f1f1f' : '#ffffff',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
      }}>
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          locale={{
            emptyText: (
              <Empty
                description="暂无角色数据"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ),
          }}
        />
      </Card>

      {/* 新增/编辑角色弹窗 */}
      <Modal
        title={editingRole ? '编辑角色' : '新增角色'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="角色代码"
                rules={[{ required: true, message: '请输入角色代码' }]}
              >
                <Input placeholder="请输入角色代码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="角色描述"
          >
            <TextArea rows={3} placeholder="请输入角色描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
                initialValue="active"
              >
                <Select placeholder="请选择状态">
                  <Option value="active">启用</Option>
                  <Option value="disabled">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_system"
                label="系统角色"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingRole ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分配权限弹窗 */}
      <Modal
        title="分配权限"
        open={permissionModalVisible}
        onCancel={() => setPermissionModalVisible(false)}
        onOk={handlePermissionSubmit}
        width={600}
      >
        <Tree
          checkable
          treeData={permissionTreeData}
          fieldNames={{ title: 'display_name', key: 'id', children: 'children' }}
          checkedKeys={selectedPermissions}
          onCheck={(checkedKeys) => setSelectedPermissions(checkedKeys as number[])}
          defaultExpandAll
        />
      </Modal>

      {/* 分配用户弹窗 */}
      <Modal
        title="分配用户到角色"
        open={assignModalVisible}
        onCancel={() => setAssignModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={assignForm}
          layout="vertical"
          onFinish={handleAssignSubmit}
        >
          <Form.Item
            name="user_ids"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择要分配的用户"
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              <Option value={1}>张三 (zhangsan)</Option>
              <Option value={2}>李四 (lisi)</Option>
              <Option value={3}>王五 (wangwu)</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setAssignModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                分配
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限分配组件 */}
      <PermissionAssigner
        visible={permissionAssignerVisible}
        onCancel={handlePermissionAssignerCancel}
        onOk={handlePermissionAssignerOk}
        title={`分配权限 - ${roles.find(r => r.id === selectedRoleId)?.display_name || ''}`}
        initialSelectedPermissions={selectedPermissions}
        loading={permissionAssignerLoading}
      />
    </div>
  );
};

export default RolePage; 