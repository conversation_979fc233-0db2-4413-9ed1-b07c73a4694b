import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Tooltip,
  Popconfirm,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import {
  listAPIResources,
  createAPIResource,
  updateAPIResource,
  deleteAPIResource,
  type CreateAPIResourceRequest,
  type UpdateAPIResourceRequest,
  type ListAPIResourcesRequest,
} from '../../services/api-resource';
import { showError, showSuccess } from '../../utils/messageManager';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { APIResource } from '../../services/resource';
import {
  getApplications,
  type Application,
} from '../../services/application';
import { TenantService } from '../../services/tenant';
import type { Tenant } from '../../types/tenant';

import { SUCCESS } from '../../constants/errorCodes';

const { Option } = Select;
const { Text, Title } = Typography;
const { TextArea } = Input;

// 租户和应用选择组件
interface TenantAppSelectorProps {
  selectedTenantId: number | null;
  selectedAppId: string | null;
  selectedInternalAppId: number | null;
  onTenantChange: (tenantId: number | null) => void;
  onAppChange: (appId: string | null, internalAppId: number | null) => void;
}

const TenantAppSelector: React.FC<TenantAppSelectorProps> = ({
  selectedTenantId,
  selectedAppId,
  selectedInternalAppId,
  onTenantChange,
  onAppChange,
}) => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [apps, setApps] = useState<Application[]>([]);
  const [loadingTenants, setLoadingTenants] = useState(false);
  const [loadingApps, setLoadingApps] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // 加载租户列表
  const loadTenants = useCallback(async () => {
    setLoadingTenants(true);
    setError(null);
    try {
      const response = await TenantService.listTenants();
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        setError(`加载租户失败: ${response.message}`);
        return;
      }

      // 处理成功响应
      setTenants(response.data || []);
    } catch (error) {
      console.error('加载租户失败:', error);
      setError('加载租户失败，请稍后重试');
    } finally {
      setLoadingTenants(false);
    }
  }, []);

  // 统一的应用加载逻辑 - 只在租户选择后触发
  useEffect(() => {
    const loadData = async () => {
      // 只有选择了租户才加载应用
      if (!selectedTenantId) {
        setApps([]);
        return;
      }

      // 防止重复请求
      if (loadingApps) {
        return;
      }

      setLoadingApps(true);
      setError(null);
      try {
        // 构建请求参数 - 始终传递选择的租户ID
        const params: any = {
          tenant_id: selectedTenantId
        };

        const response = await getApplications(params);
        
        // 处理业务错误 - 检查 code 字段
        if (response.code !== SUCCESS) {
          setError(`加载应用失败: ${response.message}`);
          setApps([]);
          return;
        }

        // 处理成功响应
        setApps(response.data || []);
        // 如果只有一个应用，自动选择
        if (response.data && response.data.length === 1 && !selectedAppId) {
          const app = response.data[0];
          onAppChange(app.app_name, app.internal_app_id);
        }
      } catch (error) {
        console.error('加载应用失败:', error);
        setError('加载应用失败，请稍后重试');
        setApps([]);
      } finally {
        setLoadingApps(false);
      }
    };

    loadData();
  }, [selectedTenantId]); // 只依赖 selectedTenantId

  const handleTenantChange = useCallback((tenantId: number) => {
    onTenantChange(tenantId);
    onAppChange(null, null); // 清空应用选择
  }, [onTenantChange, onAppChange]);

  const handleAppChange = useCallback((appId: string) => {
    const app = apps.find(a => a.app_name === appId);
    onAppChange(appId, app ? app.internal_app_id : null);
  }, [apps, onAppChange]);

  return (
    <Card 
      size="small"
      style={{ marginBottom: 16 }}
    >
      {error && (
        <div style={{ marginBottom: 16, color: '#ff4d4f' }}>
          <Text type="danger">{error}</Text>
        </div>
      )}
      
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 16, 
        flexWrap: 'nowrap',
        overflow: 'hidden'
      }}>
        {/* 租户选择器 - 始终显示 */}
        <div style={{ 
          flex: '1 1 300px',
          minWidth: 250,
          maxWidth: 400
        }}>
          <Form.Item label="选择租户" style={{ marginBottom: 0 }}>
            <Select
              placeholder="请选择租户"
              value={selectedTenantId}
              onChange={handleTenantChange}
              loading={loadingTenants}
              allowClear
              showSearch
              optionFilterProp="children"
              onFocus={loadTenants}
              style={{ width: '100%' }}
            >
              {tenants.map(tenant => (
                <Option key={tenant.id} value={tenant.id}>
                  {tenant.tenant_name} ({tenant.tenant_code})
                </Option>
              ))}
            </Select>
          </Form.Item>
        </div>
        
        {/* 应用选择器 - 只有选择租户后才显示 */}
        {selectedTenantId && (
          <div style={{ 
            flex: '0 1 250px',
            minWidth: 200
          }}>
            <Form.Item label="选择应用" style={{ marginBottom: 0 }}>
              <Select
                placeholder="请选择应用"
                value={selectedAppId}
                onChange={handleAppChange}
                loading={loadingApps}
                allowClear
                showSearch
                optionFilterProp="children"
                style={{ width: '100%' }}
              >
                {apps.map(app => (
                  <Option key={app.internal_app_id} value={app.app_name}>
                    {app.app_name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        )}

        {/* 当前租户信息显示 */}
        {selectedTenantId && (
          <div style={{ 
            flex: '0 0 auto',
            minWidth: 100
          }}>
            <Form.Item label="当前租户" style={{ marginBottom: 0 }}>
              <Text type="secondary" style={{ fontSize: 12, whiteSpace: 'nowrap' }}>
                ID: {selectedTenantId}
              </Text>
            </Form.Item>
          </div>
        )}

        {/* 刷新按钮 */}
        <div style={{ 
          flex: '0 0 auto',
          marginLeft: 'auto'
        }}>
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={() => {
              const loadData = async () => {
                if (!selectedTenantId) return;
                
                setLoadingApps(true);
                try {
                  const response = await getApplications({ tenant_id: selectedTenantId });
                  if (response.code === SUCCESS) {
                    setApps(response.data || []);
                  }
                } catch (error) {
                  console.error('刷新应用列表失败:', error);
                } finally {
                  setLoadingApps(false);
                }
              };
              loadData();
            }}
            loading={loadingApps}
            title="刷新应用列表"
            disabled={!selectedTenantId}
          />
        </div>
      </div>
    </Card>
  );
};

// 服务名称选项
const serviceNames = [
  { value: 'user-service', label: '用户服务' },
  { value: 'email-service', label: '邮件服务' },
  { value: 'file-service', label: '文件服务' },
  { value: 'auth-service', label: '认证服务' },
  { value: 'notification-service', label: '通知服务' },
  { value: 'payment-service', label: '支付服务' },
  { value: 'order-service', label: '订单服务' },
  { value: 'product-service', label: '产品服务' },
];

// 请求类型选项
const requestTypes = [
  { value: 'json', label: 'JSON' },
  { value: 'form', label: 'Form' },
  { value: 'file', label: 'File' },
  { value: 'text', label: 'Text' },
  { value: 'stream', label: 'Stream' },
  { value: 'xml', label: 'XML' },
  { value: 'binary', label: 'Binary' },
];

// 响应类型选项
const responseTypes = [
  { value: 'json', label: 'JSON' },
  { value: 'html', label: 'HTML' },
  { value: 'xml', label: 'XML' },
  { value: 'stream', label: 'Stream' },
  { value: 'file', label: 'File' },
  { value: 'text', label: 'Text' },
  { value: 'binary', label: 'Binary' },
];

// HTTP方法选项
const httpMethods = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' },
];

interface ApiManagementPageProps {}

const ApiManagementPage: React.FC<ApiManagementPageProps> = () => {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  
  // 租户和应用选择状态
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [selectedAppId, setSelectedAppId] = useState<string | null>(null);
  const [selectedInternalAppId, setSelectedInternalAppId] = useState<number | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [apiList, setApiList] = useState<APIResource[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingApi, setEditingApi] = useState<APIResource | null>(null);
  const [form] = Form.useForm();

  const actionButtonStyle = {
    color: isDarkMode ? '#40a9ff' : '#1890ff',
    fontWeight: 500,
  };

  const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

  // 统计数据
  const stats = [
    { title: '总API数', value: total, icon: <ApiOutlined />, color: '#1890ff' },
    { title: '启用API', value: Array.isArray(apiList) ? apiList.filter(api => api.status === 'active').length : 0, icon: <CheckCircleOutlined />, color: '#52c41a' },
    { title: '禁用API', value: Array.isArray(apiList) ? apiList.filter(api => api.status !== 'active').length : 0, icon: <CloseCircleOutlined />, color: '#ff4d4f' },
    { title: '服务数量', value: Array.isArray(apiList) ? new Set(apiList.map(api => api.service_name).filter(Boolean)).size : 0, icon: <AppstoreOutlined />, color: '#722ed1' },
  ];

  // 获取API列表
  const fetchApiList = useCallback(async () => {
    if (!selectedInternalAppId) {
      return;
    }
    setLoading(true);
    try {
      const params: ListAPIResourcesRequest = {
        internal_app_id: selectedInternalAppId,
        page: currentPage,
        page_size: pageSize,
        keyword: searchKeyword,
        sort_by: 'created_at',
        sort_order: 'desc',
      };
      
      const response = await listAPIResources(params);
      if (response.code === SUCCESS && response.data) {
        // 新的响应结构：数据在 data 字段，分页信息在 meta.pagination 中
        setApiList(response.data || []);
        setTotal(response.meta?.pagination?.total || 0);
      } else {
        showError('获取API资源列表失败');
      }
    } catch (error) {
      console.error('Failed to load API resources:', error);
      showError('获取API资源列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedInternalAppId, currentPage, pageSize, searchKeyword]);

  useEffect(() => {
    if (selectedInternalAppId) {
      fetchApiList();
    }
  }, [selectedInternalAppId, fetchApiList]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1);
  };

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(e.target.value);
    setCurrentPage(1);
  };

  // 处理租户变更
  const handleTenantChange = (tenantId: number | null) => {
    setSelectedTenantId(tenantId);
    setApiList([]);
    setTotal(0);
    setCurrentPage(1);
  };

  // 处理应用变更
  const handleAppChange = (appId: string | null, internalAppId: number | null) => {
    setSelectedAppId(appId);
    setSelectedInternalAppId(internalAppId);
    
    // 清空之前的数据
    setApiList([]);
    setTotal(0);
    setCurrentPage(1);
  };

  // 处理新增API
  const handleAddApi = () => {
    setEditingApi(null);
    form.resetFields();
    form.setFieldsValue({
      api_method: 'GET',
      request_type: 'json',
      response_type: 'json',
      is_public: false,
      status: 'active',
    });
    setModalVisible(true);
  };

  // 处理编辑API
  const handleEditApi = (record: APIResource) => {
    setEditingApi(record);
    form.setFieldsValue({
      name: record.name,
      display_name: record.display_name,
      description: record.description,
      service_name: record.service_name,
      request_type: record.request_type,
      response_type: record.response_type,
      api_method: record.api_method,
      path: record.path,
      content_type: record.content_type,
      is_public: record.is_public,
      status: record.status,
    });
    setModalVisible(true);
  };

  // 处理删除API
  const handleDeleteApi = async (id: number) => {
    try {
      const response = await deleteAPIResource(id);
      if (response.code === SUCCESS) {
        showSuccess('删除API资源成功');
        fetchApiList();
      } else {
        showError('删除API资源失败');
      }
    } catch (error) {
      console.error('Failed to delete API resource:', error);
      showError('删除API资源失败');
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      if (editingApi) {
        // 更新API
        const updateData: UpdateAPIResourceRequest = {
          id: editingApi.id,
          ...values,
        };
        const response = await updateAPIResource(updateData);
        if (response.code === SUCCESS) {
          showSuccess('更新API资源成功');
          setModalVisible(false);
          fetchApiList();
        } else {
          showError('更新API资源失败');
        }
      } else {
        // 创建API
        const createData: CreateAPIResourceRequest = values;
        const response = await createAPIResource(createData);
        if (response.code === SUCCESS) {
          showSuccess('创建API资源成功');
          setModalVisible(false);
          fetchApiList();
        } else {
          showError('创建API资源失败');
        }
      }
    } catch (error) {
      console.error('Failed to save API resource:', error);
      showError('保存API资源失败');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: 'API信息',
      key: 'api_info',
      render: (record: APIResource) => (
        <div>
          <div style={{ fontWeight: 500, color: isDarkMode ? '#ffffff' : '#262626' }}>
            {record.display_name}
          </div>
          <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
            {record.name}
          </div>
        </div>
      ),
    },
    {
      title: '服务信息',
      key: 'service_info',
      render: (record: APIResource) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Tag color="blue">{record.service_name || '-'}</Tag>
          </div>
          <div>
            <Tag color="cyan">{record.request_type || '-'}</Tag>
            <Tag color="magenta" style={{ marginLeft: 4 }}>{record.response_type || '-'}</Tag>
          </div>
        </div>
      ),
    },
    {
      title: '接口信息',
      key: 'interface_info',
      render: (record: APIResource) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            {(() => {
              const colorMap: Record<string, string> = {
                GET: 'green',
                POST: 'blue',
                PUT: 'orange',
                DELETE: 'red',
                PATCH: 'purple',
              };
              return <Tag color={colorMap[record.api_method || ''] || 'default'}>{record.api_method || '-'}</Tag>;
            })()}
          </div>
          <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
            <Text code>{record.path || '-'}</Text>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: APIResource) => (
        <Tag color={record.status === 'active' ? 'green' : 'red'}>
          {record.status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },

    {
      title: '创建时间',
      key: 'created_at',
      width: 120,
      render: (record: APIResource) => (
        <Text style={{ fontSize: 12 }}>
          {new Date(record.created_at).toLocaleDateString()}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (record: APIResource) => (
        <Space size="small">
          <Tooltip title="编辑" color={tooltipColor}>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditApi(record)}
              style={actionButtonStyle}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个API吗？"
            onConfirm={() => handleDeleteApi(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除" color={tooltipColor}>
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                style={actionButtonStyle}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="api-management-page">
      {/* 租户和应用选择组件 */}
      <TenantAppSelector
        selectedTenantId={selectedTenantId}
        selectedAppId={selectedAppId}
        selectedInternalAppId={selectedInternalAppId}
        onTenantChange={handleTenantChange}
        onAppChange={handleAppChange}
      />
      
      {/* 只有选择了应用才显示主要内容 */}
      {selectedInternalAppId ? (
        <div>
          {/* 页面标题区域 - 统计展示 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
              >
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                    fontSize: 18,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{
                    fontSize: 24,
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stat.value.toLocaleString()}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchApiList}
              loading={loading}
              disabled={!selectedInternalAppId}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddApi}
              disabled={!selectedInternalAppId}
            >
              新增API
            </Button>
          </Space>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索API名称或路径"
              value={searchKeyword}
              onChange={handleSearchChange}
              onSearch={handleSearch}
              allowClear
            />
          </Col>
        </Row>
      </Card>

      {/* 表格区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Table
          columns={columns}
          dataSource={apiList}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑API模态框 */}
      <Modal
        title={editingApi ? '编辑API' : '新增API'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="API标识"
                rules={[
                  { required: true, message: '请输入API标识' },
                  { pattern: /^[a-z0-9_-]+$/, message: '只能包含小写字母、数字、下划线和连字符' },
                ]}
              >
                <Input placeholder="请输入API标识" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="display_name"
                label="API名称"
                rules={[{ required: true, message: '请输入API名称' }]}
              >
                <Input placeholder="请输入API名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="service_name"
                label="服务名称"
                rules={[{ required: true, message: '请选择服务名称' }]}
              >
                <Select placeholder="请选择服务名称">
                  {serviceNames.map(service => (
                    <Option key={service.value} value={service.value}>
                      {service.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="api_method"
                label="HTTP方法"
                rules={[{ required: true, message: '请选择HTTP方法' }]}
              >
                <Select placeholder="请选择HTTP方法">
                  {httpMethods.map(method => (
                    <Option key={method.value} value={method.value}>
                      {method.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="path"
            label="API路径"
            rules={[{ required: true, message: '请输入API路径' }]}
          >
            <Input placeholder="/api/example/path" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="request_type"
                label="请求类型"
              >
                <Select placeholder="请选择请求类型" allowClear>
                  {requestTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="response_type"
                label="响应类型"
              >
                <Select placeholder="请选择响应类型" allowClear>
                  {responseTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="content_type" label="Content-Type">
                <Input placeholder="如：application/json" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="状态">
                <Select placeholder="状态">
                  <Option value="active">激活</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="is_public" label="是否公开" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>



          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入API描述"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingApi ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
        </div>
      ) : (
        /* 未选择应用时显示提示信息 */
        <div style={{ 
          height: '400px', 
          display: 'flex', 
          flexDirection: 'column', 
          justifyContent: 'center', 
          alignItems: 'center',
          color: '#8c8c8c',
          marginTop: '40px'
        }}>
          <ApiOutlined style={{ fontSize: 64, marginBottom: 24, opacity: 0.6 }} />
          <Text type="secondary" style={{ fontSize: 18, textAlign: 'center' }}>
            请先选择租户和应用<br />
            <Text type="secondary" style={{ fontSize: 14, marginTop: 8 }}>
              选择应用后将显示API管理界面
            </Text>
          </Text>
        </div>
      )}
    </div>
  );
};

export default ApiManagementPage; 