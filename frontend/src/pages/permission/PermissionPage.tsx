import React, {useState, useEffect, useCallback} from 'react';
import { 
  Table, 
  Button, 
  Space, 
    Modal,
    Form,
    Input,
    Select,
    Card,
    Row,
    Col,
    Tag,
  Typography,
    Tooltip,
    Empty,

} from 'antd';
import { SUCCESS } from '../../constants/errorCodes';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
    KeyOutlined,
  ReloadOutlined,
    SafetyCertificateOutlined,
  SettingOutlined,
    LockOutlined,
    UnlockOutlined
} from '@ant-design/icons';
import {
    getPermissions,
    createPermission,
    updatePermission,
    deletePermission,
    getPermissionStats,
    type Permission,
    type ListPermissionRequest,
    type PermissionStats
} from '../../services/permission';
import {useTheme} from '../../contexts/ThemeContext';
import {showAPIError} from '../../utils/errorHandler';
import { showError, showSuccess } from '../../utils/messageManager';

const {Text} = Typography;
const {TextArea} = Input;
const {Option} = Select;

const PermissionPage: React.FC = () => {
    const {isDarkMode} = useTheme();
    const [permissions, setPermissions] = useState<Permission[]>([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
    const [stats, setStats] = useState<PermissionStats | null>(null);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [searchParams, setSearchParams] = useState<ListPermissionRequest>({
        page: 1,
        size: 10,
    });
    const [form] = Form.useForm();

    // 权限操作类型选项
    const actionTypes = [
        {value: 'create', label: '创建'},
        {value: 'read', label: '查看'},
        {value: 'update', label: '更新'},
        {value: 'delete', label: '删除'},
        {value: 'export', label: '导出'},
        {value: 'import', label: '导入'},
        {value: 'approve', label: '审批'},
        {value: 'reject', label: '拒绝'},
        {value: 'assign', label: '分配'},
        {value: 'revoke', label: '撤销'},
    ];

    // 权限范围选项
    const scopeTypes = [
        {value: 'all', label: '全部权限'},
        {value: 'self', label: '普通权限'},
    ];

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const permissionsRes = await getPermissions(searchParams);

            if (permissionsRes?.code === 0) {
                setPermissions(permissionsRes.data || []);
                setPagination(prev => ({
                    ...prev,
                    current: permissionsRes.meta?.pagination?.page || 1,
                    total: permissionsRes.meta?.pagination?.total || 0,
                }));
            } else {
                console.warn('获取权限列表失败:', permissionsRes?.message);
                setPermissions([]);
            }
        } catch (error) {
            showAPIError(error);
        } finally {
            setLoading(false);
        }
    }, [searchParams]);

    const fetchStats = useCallback(async () => {
        try {
            const statsData = await getPermissionStats();
            if (statsData.code === 0) {
                setStats(statsData.data);
            } else {
                console.warn('获取统计数据失败:', statsData.message);
            }
        } catch (error) {
            showAPIError(error);
        }
    }, []);

    useEffect(() => {
        fetchData();
        fetchStats();
    }, [fetchData, fetchStats]);

    const handleAdd = () => {
        setEditingPermission(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEdit = (record: Permission) => {
        setEditingPermission(record);
        form.setFieldsValue({
            name: record.name,
            display_name: record.display_name,
            description: record.description,
            action: record.action,
            scope: record.scope,
            status: record.status,
        });
        setModalVisible(true);
    };

    const handleDelete = async (id: number) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除这个权限吗？删除后无法恢复。',
            onOk: async () => {
                try {
                    const response = await deletePermission(id);
                    if (response.code === SUCCESS) {
                        showSuccess('权限删除成功');
                        fetchData();
                        fetchStats();
                    } else {
                        showError(response.message || '权限删除失败', 'permission-delete-error');
                    }
                } catch (error) {
                    showAPIError(error);
                }
            },
        });
    };

    const handleSubmit = async (values: any) => {
        try {
            if (editingPermission) {
                const response = await updatePermission(editingPermission.id, values);
                if (response.code === SUCCESS) {
                    showSuccess('权限更新成功');
                } else {
                    showError(response.message || '权限更新失败', 'permission-update-error');
                    return;
                }
            } else {
                const response = await createPermission(values);
                if (response.code === SUCCESS) {
                    showSuccess('权限创建成功');
                } else {
                    showError(response.message || '权限创建失败', 'permission-create-error');
                    return;
                }
            }
            setModalVisible(false);
            fetchData();
            fetchStats();
        } catch (error) {
            showAPIError(error);
        }
    };

    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
        setSearchParams(prev => ({
            ...prev,
            page: pagination.current,
            size: pagination.pageSize,
        }));
    };

    const handleSearch = (value: string) => {
        setSearchParams(prev => ({
            ...prev,
            keyword: value,
            page: 1,
        }));
    };

    const handleFilter = (field: string, value: any) => {
        setSearchParams(prev => ({
            ...prev,
            [field]: value,
            page: 1,
        }));
    };

    const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

    const columns = [
        {
            title: '权限名称',
            dataIndex: 'display_name',
            key: 'display_name',
            render: (text: string, record: Permission) => (
                <div>
                    <div style={{fontWeight: 500, color: isDarkMode ? '#ffffff' : '#262626'}}>
                        {text}
                    </div>
                    <div style={{fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c'}}>
                        {record.name}
                    </div>
                </div>
            ),
        },
        {
            title: '操作类型',
            dataIndex: 'action',
            key: 'action',
            width: 120,
            render: (action: string) => {
                const actionMap: { [key: string]: { color: string; label: string } } = {
                    create: {color: 'green', label: '创建'},
                    read: {color: 'blue', label: '查看'},
                    update: {color: 'orange', label: '更新'},
                    delete: {color: 'red', label: '删除'},
                    export: {color: 'cyan', label: '导出'},
                    import: {color: 'purple', label: '导入'},
                    approve: {color: 'green', label: '审批'},
                    reject: {color: 'red', label: '拒绝'},
                    assign: {color: 'blue', label: '分配'},
                    revoke: {color: 'orange', label: '撤销'},
                };
                const config = actionMap[action] || {color: 'default', label: action};
                return <Tag color={config.color}>{config.label}</Tag>;
            },
        },
        {
            title: '权限范围',
            dataIndex: 'scope',
            key: 'scope',
            width: 100,
            render: (scope: string, record: Permission) => {
                const scopeMap: { [key: string]: { color: string; label: string } } = {
                    all: {color: 'red', label: '全部权限'},
                    self: {color: 'blue', label: '普通权限'},
                };
                const config = scopeMap[scope] || {color: 'default', label: scope};
                return (
                    <Tag color={config.color}>
                        {record.scope_display_name || config.label}
                    </Tag>
                );
            },
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
            render: (text: string) => (
                <Text style={{color: isDarkMode ? '#8c8c8c' : '#8c8c8c'}}>
                    {text || '暂无描述'}
                </Text>
            ),
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (status: string) => (
                <Tag color={status === 'active' ? 'green' : 'red'}
                     icon={status === 'active' ? <UnlockOutlined/> : <LockOutlined/>}>
                    {status === 'active' ? '启用' : '禁用'}
                </Tag>
            ),
        },
        {
            title: '系统权限',
            dataIndex: 'is_system',
            key: 'is_system',
            width: 100,
            render: (isSystem: boolean) => (
                <Tag color={isSystem ? 'blue' : 'default'}>
                    {isSystem ? '是' : '否'}
                </Tag>
            ),
        },
        {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            width: 120,
            render: (date: string) => date,
        },
        {
            title: '操作',
            key: 'action',
            width: 150,
            render: (_: any, record: Permission) => (
                <Space size="small">
                    <Tooltip title="编辑" color={tooltipColor}>
                        <Button type="text" size="small" icon={<EditOutlined/>} onClick={() => handleEdit(record)}/>
                    </Tooltip>
                    {!record.is_system && (
                        <Tooltip title="删除" color={tooltipColor}>
                            <Button type="text" size="small" danger icon={<DeleteOutlined/>}
                                    onClick={() => handleDelete(record.id)}/>
                        </Tooltip>
                    )}
                </Space>
            ),
        },
    ];

    return (
        <div className="permission-page">
            {/* 页面标题区域 - 统计展示 */}
            <div style={{marginBottom: 24}}>
                <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                    {/* 统计信息 */}
                    {stats && (
                        <div style={{display: 'flex', alignItems: 'center', gap: 32}}>
                            <div style={{display: 'flex', alignItems: 'center', gap: 12}}>
                                <div
                                    style={{
                                        width: 40,
                                        height: 40,
                                        borderRadius: 8,
                                        background: '#1890ff15',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#1890ff',
                                        fontSize: 18,
                                    }}
                                >
                                    <SafetyCertificateOutlined/>
                                </div>
                                <div>
                                    <div style={{
                                        fontSize: 24,
                                        fontWeight: 600,
                                        color: isDarkMode ? '#ffffff' : '#262626',
                                        lineHeight: 1,
                                    }}>
                                        {stats.total_permissions?.toLocaleString() || 0}
                                    </div>
                                    <div style={{
                                        fontSize: 12,
                                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                                        marginTop: 2,
                                    }}>
                                        总权限数
                                    </div>
                                </div>
                            </div>

                            <div style={{display: 'flex', alignItems: 'center', gap: 12}}>
                                <div
                                    style={{
                                        width: 40,
                                        height: 40,
                                        borderRadius: 8,
                                        background: '#52c41a15',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#52c41a',
                                        fontSize: 18,
                                    }}
                                >
                                    <UnlockOutlined/>
                                </div>
                                <div>
                                    <div style={{
                                        fontSize: 24,
                                        fontWeight: 600,
                                        color: isDarkMode ? '#ffffff' : '#262626',
                                        lineHeight: 1,
                                    }}>
                                        {stats.active_permissions?.toLocaleString() || 0}
                                    </div>
                                    <div style={{
                                        fontSize: 12,
                                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                                        marginTop: 2,
                                    }}>
                                        启用权限
                                    </div>
                                </div>
                            </div>

                            <div style={{display: 'flex', alignItems: 'center', gap: 12}}>
                                <div
                                    style={{
                                        width: 40,
                                        height: 40,
                                        borderRadius: 8,
                                        background: '#722ed115',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#722ed1',
                                        fontSize: 18,
                                    }}
                                >
                                    <SettingOutlined/>
                                </div>
                                <div>
                                    <div style={{
                                        fontSize: 24,
                                        fontWeight: 600,
                                        color: isDarkMode ? '#ffffff' : '#262626',
                                        lineHeight: 1,
                                    }}>
                                        {stats.system_permissions?.toLocaleString() || 0}
                                    </div>
                                    <div style={{
                                        fontSize: 12,
                                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                                        marginTop: 2,
                                    }}>
                                        系统权限
                                    </div>
                                </div>
                            </div>

                            <div style={{display: 'flex', alignItems: 'center', gap: 12}}>
                                <div
                                    style={{
                                        width: 40,
                                        height: 40,
                                        borderRadius: 8,
                                        background: '#fa8c1615',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#fa8c16',
                                        fontSize: 18,
                                    }}
                                >
                                    <KeyOutlined/>
                                </div>
                                <div>
                                    <div style={{
                                        fontSize: 24,
                                        fontWeight: 600,
                                        color: isDarkMode ? '#ffffff' : '#262626',
                                        lineHeight: 1,
                                    }}>
                                        {stats.action_count?.toLocaleString() || 0}
                                    </div>
                                    <div style={{
                                        fontSize: 12,
                                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                                        marginTop: 2,
                                    }}>
                                        操作类型
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 操作按钮 */}
                    <Space>
                        <Button type="primary" icon={<PlusOutlined/>} onClick={handleAdd}>
                            新增权限
                        </Button>
                    </Space>
                </div>
            </div>

            {/* 搜索和筛选区域 */}
            <Card
                style={{
                    borderRadius: 12,
                    borderStyle: 'none',
                    background: isDarkMode ? '#1f1f1f' : '#ffffff',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                    border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
                    marginBottom: 24,
                }}
                styles={{body: {padding: 20}}}
            >
                <Row gutter={[16, 16]} align="middle">
                    <Col xs={24} sm={12} md={8} lg={6}>
                        <Input.Search
                            placeholder="搜索权限名称或代码"
                            onSearch={handleSearch}
                            allowClear
                        />
                    </Col>
                    <Col xs={24} sm={12} md={8} lg={6}>
                        <Select
                            placeholder="选择操作类型"
                            allowClear
                            style={{width: '100%'}}
                            onChange={(value) => handleFilter('action', value)}
                        >
                            {actionTypes.map(type => (
                                <Option key={type.value} value={type.value}>{type.label}</Option>
                            ))}
                        </Select>
                    </Col>
                    <Col xs={24} sm={12} md={8} lg={6}>
                        <Select
                            placeholder="操作类型"
                            allowClear
                            style={{width: '100%'}}
                            onChange={(value) => handleFilter('action', value)}
                        >
                            {actionTypes.map(type => (
                                <Option key={type.value} value={type.value}>{type.label}</Option>
                            ))}
                        </Select>
                    </Col>
                    <Col xs={24} sm={12} md={8} lg={6}>
                        <Space>
                            <Button icon={<ReloadOutlined/>} onClick={fetchData}>
                                刷新
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Card>

            {/* 权限表格 */}
            <div style={{marginBottom: 24}}>
                <Card
                    style={{
                        borderRadius: 12,
                        borderStyle: 'none',
                        background: isDarkMode ? '#1f1f1f' : '#ffffff',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                        border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
                    }}
                    styles={{body: {padding: 0}}}
                >
                    <Table
                        columns={columns}
                        dataSource={permissions}
                        loading={loading}
                        rowKey="id"
                        pagination={{
                            total: pagination.total,
                            current: pagination.current,
                            pageSize: pagination.pageSize,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range) =>
                                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                        }}
                        onChange={handleTableChange}
                        style={{
                            background: 'transparent',
                        }}
                    />
                </Card>
            </div>



            {/* 新增/编辑权限模态框 */}
            <Modal
                title={
                    <div style={{display: 'flex', alignItems: 'center', gap: 8}}>
                        <SafetyCertificateOutlined style={{color: '#1890ff'}}/>
                        <span>{editingPermission ? '编辑权限' : '新增权限'}</span>
                    </div>
                }
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={null}
                width={600}
                style={{
                    borderRadius: 12,
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                    style={{marginTop: 24}}
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="name"
                                label="权限代码"
                                rules={[
                                    {required: true, message: '请输入权限代码'},
                                    {pattern: /^[a-z][a-z0-9_:]*$/, message: '权限代码格式不正确'},
                                ]}
                            >
                                <Input placeholder="例如: user:read"/>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="display_name"
                                label="权限名称"
                                rules={[{required: true, message: '请输入权限名称'}]}
                            >
                                <Input placeholder="例如: 用户查看"/>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="action"
                                label="操作类型"
                                rules={[{required: true, message: '请选择操作类型'}]}
                            >
                                <Select placeholder="请选择操作类型">
                                    {actionTypes.map(type => (
                                        <Option key={type.value} value={type.value}>{type.label}</Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="scope"
                                label="权限范围"
                                rules={[{required: true, message: '请选择权限范围'}]}
                            >
                                <Select placeholder="请选择权限范围">
                                    {scopeTypes.map(scope => (
                                        <Option key={scope.value} value={scope.value}>{scope.label}</Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="description"
                        label="权限描述"
                    >
                        <TextArea rows={3} placeholder="请输入权限描述"/>
                    </Form.Item>

                    {editingPermission && (
                        <Form.Item
                            name="status"
                            label="状态"
                            rules={[{required: true, message: '请选择状态'}]}
                        >
                            <Select placeholder="请选择状态">
                                <Option value="active">启用</Option>
                                <Option value="disabled">禁用</Option>
                            </Select>
                        </Form.Item>
                    )}

                    <div style={{textAlign: 'right', marginTop: 24}}>
                        <Space>
                            <Button onClick={() => setModalVisible(false)}>
                                取消
                            </Button>
                            <Button type="primary" htmlType="submit">
                                {editingPermission ? '更新' : '创建'}
                            </Button>
                        </Space>
                    </div>
                </Form>
            </Modal>
        </div>
    );
};

export default PermissionPage; 