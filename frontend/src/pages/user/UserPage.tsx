import React, { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  Typography,
  Row,
  Col,
  Select,
  Input,
  Modal,
  Form
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  ReloadOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  ClockCircleOutlined,
  KeyOutlined,
  SafetyCertificateOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { getUsers, createUser, updateUser, User } from '../../services/user';
import { showError, showSuccess } from '../../utils/messageManager';
import { showAPIError, applyFieldErrorsToForm } from '../../utils/errorHandler';
import { SUCCESS } from '../../constants/errorCodes';
import { useTheme } from '../../contexts/ThemeContext';
import { getDepartments, Department } from '../../services/department';
import { getRoles, Role, assignRole, AssignRoleRequest } from '../../services/role';

const { Option } = Select;
const { Text } = Typography;

const UserPage: React.FC = () => {
  const { isDarkMode } = useTheme();
  
  // 核心数据状态
  const [users, setUsers] = useState<User[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  
  // 分页和搜索状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedDepartment, setSelectedDepartment] = useState<number>();
  
  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [roleModalVisible, setRoleModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  
  // 使用 useMemo 延迟创建表单实例，只在 Modal 显示时创建
  const [form] = Form.useForm();

  // 统计数据（基于实际数据计算）
  const stats = [
    { title: '总用户数', value: pagination.total, icon: <UserOutlined />, color: '#1890ff' },
    { title: '活跃用户', value: Array.isArray(users) ? users.filter(u => u.status === 'active').length : 0, icon: <TeamOutlined />, color: '#52c41a' },
    { title: '部门数量', value: Array.isArray(departments) ? departments.length : 0, icon: <ApartmentOutlined />, color: '#722ed1' },
    { title: '角色数量', value: Array.isArray(roles) ? roles.length : 0, icon: <SettingOutlined />, color: '#fa8c16' },
  ];

  // 加载数据
  useEffect(() => {
    fetchUsers();
  }, [pagination.current, pagination.pageSize, searchText, selectedStatus, selectedDepartment]);

  useEffect(() => {
    fetchDepartmentsAndRoles();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: pagination.current,
        size: pagination.pageSize,
      };
      if (searchText) params.keyword = searchText;
      if (selectedStatus) params.status = selectedStatus;
      if (selectedDepartment) params.department_id = selectedDepartment;

      const response = await getUsers(params);

      // 检查业务状态码
      if (response.code === SUCCESS) {
        setUsers(response.data || []);
        setPagination(prev => ({
          ...prev,
          total: response.meta?.pagination?.total || 0,
        }));
      } else {
        showError(response.message || '获取用户列表失败');
        setUsers([]);
      }
    } catch (error) {
      showAPIError(error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartmentsAndRoles = async () => {
    try {
      const [departmentsRes, rolesRes] = await Promise.all([
        getDepartments(),
        getRoles(),
      ]);

      // 检查部门数据的业务状态码
      if (departmentsRes?.code === SUCCESS) {
        setDepartments(departmentsRes.data || []);
      } else {
        console.warn('获取部门列表失败:', departmentsRes?.message);
        setDepartments([]);
      }

      // 检查角色数据的业务状态码
      if (rolesRes?.code === SUCCESS) {
        setRoles(rolesRes.data || []);
      } else {
        console.warn('获取角色列表失败:', rolesRes?.message);
        setRoles([]);
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  // 处理新增用户
  const handleAddUser = () => {
    setEditingUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑用户
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      real_name: user.real_name,
      phone: user.phone,
      status: user.status,
      department_id: user.department_id,
    });
    setModalVisible(true);
  };

  // 处理角色分配
  const handleAssignRoles = (user: User) => {
    setSelectedUser(user);
    setSelectedRoleIds(user.role_names?.map((_, index) => index + 1) || []);
    setRoleModalVisible(true);
  };

  // 处理角色分配提交
  const handleRoleSubmit = async () => {
    if (!selectedUser) return;

    try {
      const assignData: AssignRoleRequest = {
        role_id: selectedRoleIds[0] || 0,
        user_ids: [selectedUser.id],
      };
      const response = await assignRole(selectedRoleIds[0] || 0, assignData);
      if (response.code === SUCCESS) {
        setRoleModalVisible(false);
        fetchUsers();
        showSuccess('角色分配成功');
      } else {
        showError(response.message || '角色分配失败');
      }
    } catch (error) {
      console.error('Failed to assign roles:', error);
      showError('角色分配失败');
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    applyFieldErrorsToForm([], form);
    try {
      let res;
      if (editingUser) {
        res = await updateUser(values);
      } else {
        res = await createUser(values);
      }

      // 处理验证错误 - 检查 errors 字段
      if (res.errors && res.errors.length > 0) {
        applyFieldErrorsToForm(res.errors, form);
        return;
      }

      // 处理业务错误 - 检查 code 字段
      if (res.code !== SUCCESS) {
        showError(res.message || (editingUser ? '用户更新失败' : '用户创建失败'));
        return;
      }

      // 处理成功响应
      showSuccess(editingUser ? '用户更新成功' : '用户创建成功');
      setModalVisible(false);
      fetchUsers();
    } catch (error) {
      showAPIError(error);
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="success" icon={<CheckCircleOutlined />}>正常</Tag>;
      case 'inactive':
        return <Tag color="default" icon={<CloseCircleOutlined />}>禁用</Tag>;
      case 'pending':
        return <Tag color="processing" icon={<ClockCircleOutlined />}>待激活</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const actionButtonStyle = {
    color: isDarkMode ? '#40a9ff' : '#1890ff',
    fontWeight: 500,
  };

  const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

  // 表格列定义
  const columns = [
    {
      title: '用户信息',
      key: 'user_info',
      render: (record: User) => (
        <div>
          <div style={{
            fontSize: 14,
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.real_name}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.username}
          </div>
        </div>
      ),
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (record: User) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.email}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.phone || '未设置'}
          </div>
        </div>
      ),
    },
    {
      title: '部门职位',
      key: 'department_position',
      render: (record: User) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.department_name || '未分配'}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.position_name || '未设置'}
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      key: 'roles',
      render: (record: User) => (
        <div>
          {record.role_names && record.role_names.length > 0 ? (
            record.role_names.map((roleName, index) => (
              <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
                {roleName}
              </Tag>
            ))
          ) : (
            <Text type="secondary">未分配</Text>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: User) => getStatusTag(record.status),
    },
    {
      title: '创建时间',
      key: 'created_at',
      render: (record: User) => (
        <Text style={{ fontSize: 12 }}>
          {record.created_at}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: User) => (
        <Space size="small">
          <Tooltip title="编辑" color={tooltipColor}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditUser(record)}
              style={actionButtonStyle}
            >
              编辑
            </Button>
          </Tooltip>
          <Tooltip title="分配角色" color={tooltipColor}>
            <Button
              type="link"
              size="small"
              icon={<TeamOutlined />}
              onClick={() => handleAssignRoles(record)}
              style={actionButtonStyle}
            >
              分配角色
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="user-page">
      {/* 页面标题区域 - 改为统计展示 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
              >
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                    fontSize: 18,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{
                    fontSize: 24,
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stat.value.toLocaleString()}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
              新增用户
            </Button>
          </Space>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索用户名"
              value={searchText}
              onChange={(e) => {
                setPagination(prev => ({ ...prev, current: 1 }));
                setSearchText(e.target.value);
              }}
              onSearch={() => fetchUsers()}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="选择状态"
              value={selectedStatus}
              onChange={value => {
                setPagination(prev => ({ ...prev, current: 1 }));
                setSelectedStatus(value);
              }}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">正常</Option>
              <Option value="inactive">禁用</Option>
              <Option value="pending">待激活</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="选择部门"
              value={selectedDepartment}
              onChange={value => {
                setPagination(prev => ({ ...prev, current: 1 }));
                setSelectedDepartment(value);
              }}
              allowClear
              style={{ width: '100%' }}
            >
              {Array.isArray(departments) ? departments.map(dept => (
                <Option key={dept.id} value={dept.id}>{dept.name}</Option>
              )) : []}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={fetchUsers}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 用户表格 */}
      <div style={{ marginBottom: 24 }}>
        <Card
          style={{
            borderRadius: 12,
            borderStyle: 'none',
            background: isDarkMode ? '#1f1f1f' : '#ffffff',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          }}
          styles={{ body: { padding: 0 } }}
        >
          <Table
            columns={columns}
            dataSource={users}
            loading={loading}
            rowKey="id"
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => setPagination(prev => ({ ...prev, current: page, pageSize })),
            }}
            style={{ background: 'transparent' }}
          />
        </Card>
      </div>

      {/* 新增/编辑用户模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <CrownOutlined style={{ color: '#1890ff' }} />
            <span>{editingUser ? '编辑用户' : '新增用户'}</span>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
        style={{
          borderRadius: 12,
        }}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: 24 }}
          preserve={false}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="real_name"
                label="真实姓名"
                rules={[{ required: true, message: '请输入真实姓名' }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="department_id"
                label="所属部门"
              >
                <Select placeholder="请选择部门" allowClear>
                  {Array.isArray(departments) ? departments.map(dept => (
                    <Option key={dept.id} value={dept.id}>{dept.name}</Option>
                  )) : []}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">正常</Option>
                  <Option value="inactive">禁用</Option>
                  <Option value="pending">待激活</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="password"
                  label="密码"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码至少6个字符' },
                  ]}
                >
                  <Input.Password placeholder="请输入密码" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="confirm_password"
                  label="确认密码"
                  rules={[
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'));
                      },
                    }),
                  ]}
                >
                  <Input.Password placeholder="请确认密码" />
                </Form.Item>
              </Col>
            </Row>
          )}

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingUser ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 角色分配模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <KeyOutlined style={{ color: '#1890ff' }} />
            <span>分配角色 - {selectedUser?.real_name}</span>
          </div>
        }
        open={roleModalVisible}
        onCancel={() => setRoleModalVisible(false)}
        onOk={handleRoleSubmit}
        width={600}
        style={{
          borderRadius: 12,
        }}
      >
        <div style={{ marginTop: 24 }}>
          <div style={{ marginBottom: 16 }}>
            <Text style={{ color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
              为用户 {selectedUser?.real_name} 分配角色
            </Text>
          </div>

          <Select
            mode="multiple"
            placeholder="请选择角色"
            value={selectedRoleIds}
            onChange={setSelectedRoleIds}
            style={{ width: '100%' }}
            maxTagCount={3}
          >
            {Array.isArray(roles) ? roles.map(role => (
              <Option key={role.id} value={role.id}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
                  <span>{role.display_name}</span>
                  <Tag color={role.is_system ? 'blue' : 'default'}>
                    {role.is_system ? '系统' : '自定义'}
                  </Tag>
                </div>
              </Option>
            )) : []}
          </Select>

          <div style={{ marginTop: 16 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              提示：用户将继承所选角色的所有权限
            </Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default UserPage; 