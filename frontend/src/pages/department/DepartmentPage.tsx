import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  message,
  Row,
  Col,
  Select,
  Input,
  Modal,
  Form,
  InputNumber,
  Badge,
  Tree
} from 'antd';
import { SUCCESS } from '../../constants/errorCodes';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ReloadOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { getDepartments, getDepartmentTree, createDepartment, updateDepartment, deleteDepartment, Department } from '../../services/department';
import { showError, showSuccess } from '../../utils/messageManager';
import { showAPIError } from '../../utils/errorHandler';
import { useTheme } from '../../contexts/ThemeContext';

const { Option } = Select;

interface DepartmentWithDisplay extends Department {
  userCount?: number;
  manager?: string;
  phone?: string;
  email?: string;
  children?: DepartmentWithDisplay[];
}

// 统计数据
const stats = [
  { title: '总部门数', value: 0, icon: <ApartmentOutlined />, color: '#1890ff' },
  { title: '活跃部门', value: 0, icon: <TeamOutlined />, color: '#52c41a' },
  { title: '部门用户', value: 0, icon: <UserOutlined />, color: '#722ed1' },
  { title: '组织层级', value: 0, icon: <SettingOutlined />, color: '#fa8c16' },
];

const safeArray = (arr: any) => Array.isArray(arr) ? arr : [];

const convertToTreeData = (departments: DepartmentWithDisplay[]): any[] => {
  if (!Array.isArray(departments)) return [];
  return departments.map(dept => ({
    title: (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <span>{dept.name}</span>
        <Tag color="blue">{dept.code}</Tag>
        {dept.userCount && (
          <Badge count={dept.userCount} size="small" />
        )}
      </div>
    ),
    key: dept.id,
    children: dept.children ? convertToTreeData(dept.children) : undefined,
  }));
};

const DepartmentPage: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [departments, setDepartments] = useState<DepartmentWithDisplay[]>([]);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDepartment, setEditingDepartment] = useState<DepartmentWithDisplay | null>(null);
  const [selectedTreeKeys, setSelectedTreeKeys] = useState<React.Key[]>([]);
  const [viewMode, setViewMode] = useState<'table' | 'tree'>('table');
  const [form] = Form.useForm();

  const actionButtonStyle = {
    color: isDarkMode ? '#40a9ff' : '#1890ff',
    fontWeight: 500,
  };

  const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

  // 加载数据
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [departmentsRes, treeRes] = await Promise.all([
        getDepartments(),
        getDepartmentTree(),
      ]);
      
      // 检查部门数据的业务状态码
      if (departmentsRes?.code === 0) {
        setDepartments(safeArray(departmentsRes.data));
        // 更新统计数据
        const depts = safeArray(departmentsRes.data);
        stats[0].value = depts.length;
        stats[1].value = depts.filter(d => d.status === 'active').length;
        stats[2].value = depts.reduce((sum, d) => sum + ((d as any).userCount || 0), 0);
        stats[3].value = Math.max(...(depts.map(d => d.level) || [0]));
      } else {
        console.warn('获取部门列表失败:', departmentsRes?.message);
        setDepartments([]);
      }
      
      // 检查树形数据的业务状态码
      if (treeRes?.code === 0) {
        setTreeData(convertToTreeData(safeArray(treeRes.data)));
      } else {
        console.warn('获取部门树失败:', treeRes?.message);
        setTreeData([]);
      }
    } catch (error) {
      // 仅接口异常时才报错
      showAPIError(error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingDepartment(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: DepartmentWithDisplay) => {
    setEditingDepartment(record);
    form.setFieldsValue({
      name: record.name,
      code: record.code,
      description: record.description,
      parent_id: record.parent_id,
      sort: record.sort,
      status: record.status,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteDepartment(id);
      if (response.code === SUCCESS) {
        message.success('部门删除成功');
        fetchData();
              } else {
          showError(response.message || '部门删除失败', 'department-delete-error');
        }
      } catch (error) {
        console.error('Failed to delete department:', error);
        showError('部门删除失败', 'department-delete-error');
      }
  };

  const handleSubmit = async (values: any) => {
    try {
      let res;
      if (editingDepartment) {
        res = await updateDepartment({ ...values, id: editingDepartment.id });
        if (res.code === 0) {
          showSuccess('部门更新成功');
        } else {
          showError(res.message || '部门更新失败', 'department-update-error');
          return;
        }
      } else {
        res = await createDepartment(values);
        if (res.code === 0) {
          showSuccess('部门创建成功');
        } else {
          showError(res.message || '部门创建失败', 'department-create-error');
          return;
        }
      }
      setModalVisible(false);
      fetchData();
    } catch (error) {
      showAPIError(error);
    }
  };

  const handleTreeSelect = (selectedKeys: React.Key[]) => {
    setSelectedTreeKeys(selectedKeys);
  };

  const findDepartmentById = (depts: DepartmentWithDisplay[], id: number): DepartmentWithDisplay | null => {
    for (const dept of depts) {
      if (dept.id === id) return dept;
      if (dept.children) {
        const found = findDepartmentById(dept.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const getParentOptions = () => {
    const options = [{ value: 0, label: '顶级部门' }];
    const addOptions = (depts: DepartmentWithDisplay[], level = 0) => {
      depts.forEach(dept => {
        options.push({
          value: dept.id,
          label: '　'.repeat(level) + dept.name,
        });
        if (dept.children) {
          addOptions(dept.children, level + 1);
        }
      });
    };
    addOptions(departments);
    return options;
  };

  const columns = [
    {
      title: '部门信息',
      key: 'department_info',
      render: (record: DepartmentWithDisplay) => (
        <div>
          <div style={{ 
            fontSize: 14, 
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.name}
          </div>
          <div style={{ 
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.code}
          </div>
        </div>
      ),
    },
    {
      title: '描述',
      key: 'description',
      render: (record: DepartmentWithDisplay) => (
        <div style={{ 
          fontSize: 14,
          color: isDarkMode ? '#ffffff' : '#262626',
        }}>
          {record.description || '暂无描述'}
        </div>
      ),
    },
    {
      title: '层级',
      key: 'level',
      render: (record: DepartmentWithDisplay) => (
        <Tag color="blue">L{record.level}</Tag>
      ),
    },
    {
      title: '排序',
      key: 'sort',
      render: (record: DepartmentWithDisplay) => (
        <div style={{ 
          fontSize: 14,
          color: isDarkMode ? '#ffffff' : '#262626',
        }}>
          {record.sort}
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: DepartmentWithDisplay) => (
        <Tag color={record.status === 'active' ? 'success' : 'default'}>
          {record.status === 'active' ? '正常' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      key: 'created_at',
      render: (record: DepartmentWithDisplay) => (
        <div style={{ 
          fontSize: 12,
          color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
        }}>
          {record.created_at}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: DepartmentWithDisplay) => (
        <Space size="small">
          <Tooltip title="编辑" color={tooltipColor}>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              style={actionButtonStyle}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除" color={tooltipColor}>
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              style={actionButtonStyle}
              onClick={() => handleDelete(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="department-page">
      {/* 页面标题区域 - 改为统计展示 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div key={index} style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                    fontSize: 18,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{ 
                    fontSize: 24, 
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stat.value.toLocaleString()}
                  </div>
                  <div style={{ 
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* 操作按钮 */}
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增部门
            </Button>
          </Space>
        </div>
      </div>

      {/* 视图切换 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
        styles={{ body: { padding: 24 } }}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Button
              type={viewMode === 'table' ? 'primary' : 'default'}
              onClick={() => setViewMode('table')}
            >
              表格视图
            </Button>
            <Button
              type={viewMode === 'tree' ? 'primary' : 'default'}
              onClick={() => setViewMode('tree')}
            >
              树形视图
            </Button>
          </div>
          <Button icon={<ReloadOutlined />} onClick={fetchData}>
            刷新
          </Button>
        </div>

        {viewMode === 'table' ? (
          <Table
            columns={columns}
            dataSource={departments}
            loading={loading}
            rowKey="id"
            pagination={{
              total: departments.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            style={{
              background: 'transparent',
            }}
          />
        ) : (
          <div style={{ minHeight: 400 }}>
            <Tree
              treeData={treeData}
              selectedKeys={selectedTreeKeys}
              onSelect={handleTreeSelect}
              showLine
              showIcon
              defaultExpandAll
            />
          </div>
        )}
      </Card>

      {/* 新增/编辑部门模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <ApartmentOutlined style={{ color: '#1890ff' }} />
            <span>{editingDepartment ? '编辑部门' : '新增部门'}</span>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
        style={{
          borderRadius: 12,
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: 24 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="部门名称"
                rules={[{ required: true, message: '请输入部门名称' }]}
              >
                <Input placeholder="请输入部门名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="部门编码"
                rules={[{ required: true, message: '请输入部门编码' }]}
              >
                <Input placeholder="请输入部门编码" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="部门描述"
          >
            <Input.TextArea placeholder="请输入部门描述" rows={3} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="parent_id"
                label="上级部门"
              >
                <Select placeholder="请选择上级部门" allowClear>
                  {getParentOptions().map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sort"
                label="排序"
                rules={[{ required: true, message: '请输入排序值' }]}
              >
                <InputNumber 
                  placeholder="请输入排序值" 
                  min={0} 
                  max={999} 
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          {editingDepartment && (
            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="请选择状态">
                <Option value="active">正常</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </Form.Item>
          )}

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingDepartment ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default DepartmentPage; 