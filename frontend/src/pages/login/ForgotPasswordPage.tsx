import React, { useState } from 'react';
import { Form, Input, Button, Typography, Card, message, Steps, Space, Alert } from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  LockOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuthRedirect } from '../../hooks/useAuthRedirect';
import { forgotPassword, resetPassword, verifyResetToken } from '../../services/auth';
import { applyFieldErrorsToForm, showAPIError } from '../../utils/errorHandler';
import { showError } from '../../utils/messageManager';
import { VALIDATION_ERROR, SUCCESS } from '../../constants/errorCodes';
import './ForgotPasswordPage.css';

const { Title, Text } = Typography;
const { Step } = Steps;

const ForgotPasswordPage: React.FC = () => {
  const { isDarkMode } = useTheme();
  const navigate = useNavigate();
  const { redirectToLogin } = useAuthRedirect();
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();
  const [resetForm] = Form.useForm();
  
  // 状态管理
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState<string>('');
  const [tokenValid, setTokenValid] = useState<boolean>(false);
  
  // 从URL参数获取token
  const urlToken = searchParams.get('token');
  
  React.useEffect(() => {
    if (urlToken) {
      setToken(urlToken);
      verifyToken(urlToken);
    }
  }, [urlToken]);

  // 验证token
  const verifyToken = async (token: string) => {
    setLoading(true);
    try {
      const response = await verifyResetToken(token);
      if (response.code === SUCCESS) {
        setTokenValid(true);
        setCurrentStep(1);
      } else {
        message.error(response.message || '重置链接无效或已过期');
        setCurrentStep(0);
      }
    } catch (error) {
      message.error('验证重置链接失败');
      setCurrentStep(0);
    } finally {
      setLoading(false);
    }
  };

  // 发送重置链接
  const handleSendResetLink = async (values: any) => {
    setLoading(true);
    try {
      const response = await forgotPassword({
        username: values.username,
        email: values.email
      });
      
      if (response.code === VALIDATION_ERROR && Array.isArray((response as any).errors)) {
        applyFieldErrorsToForm((response as any).errors, form);
        return;
      }
      
      if (response.code === SUCCESS) {
        message.success('重置链接已发送到您的邮箱，请查收');
        setCurrentStep(2); // 显示成功步骤
      } else {
        showAPIError(response.message || '发送重置链接失败');
      }
    } catch (error: any) {
      console.error('Send reset link error:', error);
      showError('发送重置链接过程中发生未知错误，请稍后重试', 'forgot-password-unknown-error');
    } finally {
      setLoading(false);
    }
  };

  // 重置密码
  const handleResetPassword = async (values: any) => {
    if (values.new_password !== values.confirm_password) {
      message.error('两次输入的密码不一致');
      return;
    }
    
    setLoading(true);
    try {
      const response = await resetPassword({
        token: token,
        new_password: values.new_password,
        confirm_password: values.confirm_password
      });
      
      if (response.code === VALIDATION_ERROR && Array.isArray((response as any).errors)) {
        applyFieldErrorsToForm((response as any).errors, resetForm);
        return;
      }
      
      if (response.code === SUCCESS) {
        message.success('密码重置成功，请使用新密码登录');
        redirectToLogin({
          message: '密码重置成功，请使用新密码登录',
          clearAuth: false,
        });
      } else {
        showAPIError(response.message || '重置密码失败');
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      showError('重置密码过程中发生未知错误，请稍后重试', 'reset-password-unknown-error');
    } finally {
      setLoading(false);
    }
  };

  // 返回登录页
  const handleBackToLogin = () => {
    redirectToLogin({
      message: '返回登录页面',
      clearAuth: false,
    });
  };

  // 密码强度检查
  const validatePassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('请输入密码'));
    }
    if (value.length < 8) {
      return Promise.reject(new Error('密码长度至少8位'));
    }
    if (!/(?=.*[a-z])/.test(value)) {
      return Promise.reject(new Error('密码必须包含小写字母'));
    }
    if (!/(?=.*[A-Z])/.test(value)) {
      return Promise.reject(new Error('密码必须包含大写字母'));
    }
    if (!/(?=.*\d)/.test(value)) {
      return Promise.reject(new Error('密码必须包含数字'));
    }
    if (!/(?=.*[!@#$%^&*])/.test(value)) {
      return Promise.reject(new Error('密码必须包含特殊字符'));
    }
    return Promise.resolve();
  };

  const steps = [
    {
      title: '输入账户信息',
      description: '请输入您的用户名和邮箱'
    },
    {
      title: '重置密码',
      description: '设置新密码'
    },
    {
      title: '完成',
      description: '重置链接已发送'
    }
  ];

  return (
    <div className="forgot-password-container" style={{ 
      background: isDarkMode 
        ? 'linear-gradient(135deg, #141414 0%, #1f1f1f 50%, #262626 100%)'
        : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card 
        className="forgot-password-card"
        style={{ 
          borderRadius: 16,
          border: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: isDarkMode 
            ? '0 20px 40px rgba(0, 0, 0, 0.3)'
            : '0 20px 40px rgba(0, 0, 0, 0.1)',
          maxWidth: 480,
          width: '100%'
        }}
      >
        <Button 
          type="text" 
          icon={<ArrowLeftOutlined />} 
          onClick={handleBackToLogin}
          style={{ 
            marginBottom: 24,
            color: isDarkMode ? '#cccccc' : '#666666'
          }}
        >
          返回登录
        </Button>

        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ 
            color: isDarkMode ? '#ffffff' : '#000000',
            fontSize: 24,
            fontWeight: 600
          }}>
            忘记密码
          </Title>
          <Text style={{ 
            color: isDarkMode ? '#cccccc' : '#666666',
            fontSize: 14
          }}>
            我们将向您的邮箱发送重置链接
          </Text>
        </div>

        <Steps 
          current={currentStep} 
          items={steps}
          style={{ marginBottom: 32 }}
          progressDot
        />

        {currentStep === 0 && (
          <Form 
            form={form}
            name="forgotPassword" 
            onFinish={handleSendResetLink} 
            autoComplete="off"
            size="large"
            layout="vertical"
          >
            <Form.Item 
              name="username" 
              label="用户名"
              rules={[{ required: true, message: '请输入用户名' }]}
            > 
              <Input 
                prefix={<UserOutlined style={{ color: '#1890ff' }} />} 
                placeholder="请输入您的用户名" 
                style={{ 
                  borderRadius: 8,
                  height: 48,
                  background: isDarkMode ? '#262626' : '#fafafa',
                  border: isDarkMode ? '#434343' : '#f0f0f0'
                }}
              />
            </Form.Item>
            
            <Form.Item 
              name="email" 
              label="邮箱地址"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            > 
              <Input 
                prefix={<MailOutlined style={{ color: '#1890ff' }} />} 
                placeholder="请输入您的邮箱地址" 
                style={{ 
                  borderRadius: 8,
                  height: 48,
                  background: isDarkMode ? '#262626' : '#fafafa',
                  border: isDarkMode ? '#434343' : '#f0f0f0'
                }}
              />
            </Form.Item>

            <Alert
              message="提示"
              description="重置链接将发送到您提供的邮箱地址，请确保邮箱地址正确且可访问。"
              type="info"
              showIcon
              icon={<InfoCircleOutlined />}
              style={{ 
                marginBottom: 24,
                background: isDarkMode ? '#262626' : '#f6f8fa',
                border: isDarkMode ? '#434343' : '#e1e8ed'
              }}
            />

            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                style={{ 
                  width: '100%',
                  height: '48px',
                  fontSize: '16px',
                  borderRadius: '8px',
                }}
              >
                {loading ? '发送中...' : '发送重置链接'}
              </Button>
            </Form.Item>
          </Form>
        )}

        {currentStep === 1 && tokenValid && (
          <Form 
            form={resetForm}
            name="resetPassword" 
            onFinish={handleResetPassword} 
            autoComplete="off"
            size="large"
            layout="vertical"
          >
            <Form.Item 
              name="new_password" 
              label="新密码"
              rules={[
                { required: true, message: '请输入新密码' },
                { validator: validatePassword }
              ]}
            > 
              <Input.Password 
                prefix={<LockOutlined style={{ color: '#1890ff' }} />} 
                placeholder="请输入新密码" 
                style={{ 
                  borderRadius: 8,
                  height: 48,
                  background: isDarkMode ? '#262626' : '#fafafa',
                  border: isDarkMode ? '#434343' : '#f0f0f0'
                }}
                iconRender={(visible) => 
                  visible ? 
                    <EyeOutlined style={{ color: '#1890ff' }} /> : 
                    <EyeInvisibleOutlined style={{ color: '#999999' }} />
                }
              />
            </Form.Item>
            
            <Form.Item 
              name="confirm_password" 
              label="确认密码"
              rules={[
                { required: true, message: '请确认新密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('new_password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            > 
              <Input.Password 
                prefix={<LockOutlined style={{ color: '#1890ff' }} />} 
                placeholder="请再次输入新密码" 
                style={{ 
                  borderRadius: 8,
                  height: 48,
                  background: isDarkMode ? '#262626' : '#fafafa',
                  border: isDarkMode ? '#434343' : '#f0f0f0'
                }}
                iconRender={(visible) => 
                  visible ? 
                    <EyeOutlined style={{ color: '#1890ff' }} /> : 
                    <EyeInvisibleOutlined style={{ color: '#999999' }} />
                }
              />
            </Form.Item>

            <Alert
              message="密码要求"
              description="密码必须包含至少8个字符，包括大小写字母、数字和特殊字符。"
              type="warning"
              showIcon
              style={{ 
                marginBottom: 24,
                background: isDarkMode ? '#262626' : '#fff7e6',
                border: isDarkMode ? '#434343' : '#ffd591'
              }}
            />

            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                style={{ 
                  width: '100%',
                  height: '48px',
                  fontSize: '16px',
                  borderRadius: '8px',
                }}
              >
                {loading ? '重置中...' : '重置密码'}
              </Button>
            </Form.Item>
          </Form>
        )}

        {currentStep === 2 && (
          <div style={{ textAlign: 'center', padding: '32px 0' }}>
            <CheckCircleOutlined 
              style={{ 
                fontSize: 64, 
                color: '#52c41a',
                marginBottom: 24
              }} 
            />
            <Title level={3} style={{ 
              color: isDarkMode ? '#ffffff' : '#000000',
              marginBottom: 16
            }}>
              重置链接已发送
            </Title>
            <Text style={{ 
              color: isDarkMode ? '#cccccc' : '#666666',
              fontSize: 14,
              lineHeight: 1.6
            }}>
              我们已向您的邮箱发送了密码重置链接，请查收邮件并点击链接完成密码重置。
              <br />
              如果未收到邮件，请检查垃圾邮件文件夹或稍后重试。
            </Text>
            
            <Space style={{ marginTop: 32 }}>
              <Button onClick={handleBackToLogin}>
                返回登录
              </Button>
              <Button 
                type="primary" 
                onClick={() => setCurrentStep(0)}
              >
                重新发送
              </Button>
            </Space>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ForgotPasswordPage;
