import React from 'react';
import { Form, Input, Button, Typography, Card, message, Divider, Space } from 'antd';
import { 
  LockOutlined, 
  UserOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined,
  MailOutlined,
  TeamOutlined,
  SafetyOutlined,
  ApartmentOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuthRedirect } from '../../hooks/useAuthRedirect';
import { register } from '../../services/auth';
import { showError } from '../../utils/messageManager';
import { SUCCESS } from '../../constants/errorCodes';
import './RegisterPage.css';

const { Title, Text } = Typography;

const RegisterPage: React.FC = () => {
  const { isDarkMode, toggleTheme } = useTheme();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { redirectToLogin } = useAuthRedirect();
  const [loading, setLoading] = React.useState(false);

  const onFinish = async (values: any) => {
    if (values.password !== values.confirmPassword) {
      message.error('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    try {
      const response = await register({
        username: values.username,
        email: values.email,
        password: values.password
      });

      if (response.code === SUCCESS) {
        message.success('注册成功！请使用您的账户信息登录');
        // 使用统一的登录跳转系统
        redirectToLogin({
          message: '注册成功，请登录',
          clearAuth: false, // 注册成功不需要清除认证信息
        });
      } else {
        showError(response.message || '注册失败，请稍后重试');
      }
    } catch (error: any) {
      console.error('Register error:', error);
      showError('注册过程中发生未知错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const features = [
    { icon: <TeamOutlined />, title: '用户管理', desc: '完整的用户生命周期管理' },
    { icon: <ApartmentOutlined />, title: '组织架构', desc: '灵活的组织结构设计' },
    { icon: <SettingOutlined />, title: '权限控制', desc: '细粒度的权限管理' },
    { icon: <SafetyOutlined />, title: '安全可靠', desc: '企业级安全保障' },
  ];

  return (
    <div className="login-container" style={{ 
      background: isDarkMode 
        ? 'linear-gradient(135deg, #141414 0%, #1f1f1f 50%, #262626 100%)'
        : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
    }}>
      {/* 背景装饰 */}
      <div className="login-bg-decoration">
        <div className="floating-shape shape-1"></div>
        <div className="floating-shape shape-2"></div>
        <div className="floating-shape shape-3"></div>
      </div>

      <div className="login-content">
        <div className="login-left">
          <div className="login-hero">
            <div className="hero-logo">
              <div className="logo-icon">
                <TeamOutlined />
              </div>
              <Title level={1} style={{ 
                margin: '16px 0 8px 0',
                color: isDarkMode ? '#ffffff' : '#000000',
                fontSize: 32,
                fontWeight: 700
              }}>
                用户中心
              </Title>
              <Text style={{ 
                fontSize: 16,
                color: isDarkMode ? '#cccccc' : '#666666',
                marginBottom: 48
              }}>
                企业级用户管理系统
              </Text>
            </div>

            <div className="hero-features">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="feature-item"
                >
                  <div className="feature-icon" style={{
                    background: isDarkMode ? '#262626' : '#ffffff',
                    color: '#1890ff'
                  }}>
                    {feature.icon}
                  </div>
                  <div className="feature-content">
                    <Text strong style={{ 
                      fontSize: 14,
                      color: isDarkMode ? '#ffffff' : '#000000'
                    }}>
                      {feature.title}
                    </Text>
                    <Text style={{ 
                      fontSize: 12,
                      color: isDarkMode ? '#cccccc' : '#666666'
                    }}>
                      {feature.desc}
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="login-right">
          <div className="login-form-container">
            <Card 
              className="login-card register-card shadow-lg"
              style={{ 
                borderRadius: 16,
                border: 'none',
                background: isDarkMode ? '#1f1f1f' : '#ffffff',
                boxShadow: isDarkMode 
                  ? '0 20px 40px rgba(0, 0, 0, 0.3)'
                  : '0 20px 40px rgba(0, 0, 0, 0.1)'
              }}
            >
              <div className="login-header">
                <Title level={2} style={{ 
                  textAlign: 'center', 
                  marginBottom: 8,
                  color: isDarkMode ? '#ffffff' : '#000000',
                  fontSize: 24,
                  fontWeight: 600
                }}>
                  创建账户
                </Title>
                <Text style={{ 
                  textAlign: 'center',
                  color: isDarkMode ? '#cccccc' : '#666666',
                  fontSize: 14
                }}>
                  请填写以下信息完成注册
                </Text>
              </div>

              <Form 
                form={form}
                name="register" 
                onFinish={onFinish} 
                autoComplete="off"
                size="large"
                style={{ marginTop: 24 }}
              >
                <Form.Item 
                  name="username" 
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 3, message: '用户名至少3个字符' },
                    { max: 20, message: '用户名最多20个字符' },
                    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                  ]}
                > 
                  <Input 
                    prefix={<UserOutlined style={{ color: '#1890ff' }} />} 
                    placeholder="用户名" 
                    style={{ 
                      borderRadius: 8,
                      background: isDarkMode ? '#262626' : '#fafafa',
                      border: isDarkMode ? '#434343' : '#f0f0f0'
                    }}
                  />
                </Form.Item>
                
                <Form.Item 
                  name="email" 
                  rules={[
                    { required: true, message: '请输入邮箱地址' },
                    { type: 'email', message: '请输入有效的邮箱地址' }
                  ]}
                > 
                  <Input 
                    prefix={<MailOutlined style={{ color: '#1890ff' }} />} 
                    placeholder="邮箱地址" 
                    style={{ 
                      borderRadius: 8,
                      background: isDarkMode ? '#262626' : '#fafafa',
                      border: isDarkMode ? '#434343' : '#f0f0f0'
                    }}
                  />
                </Form.Item>
                
                <Form.Item 
                  name="password" 
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 8, message: '密码至少8个字符' },
                    { 
                      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                      message: '密码必须包含大小写字母、数字和特殊字符'
                    }
                  ]}
                > 
                  <Input.Password 
                    prefix={<LockOutlined style={{ color: '#1890ff' }} />} 
                    placeholder="密码"
                    style={{
                        borderRadius: 8,
                        background: isDarkMode ? '#262626' : '#fafafa',
                        border: isDarkMode ? '#434343' : '#f0f0f0'
                    }}
                    iconRender={(visible) => 
                      visible ? 
                        <EyeOutlined style={{ color: '#1890ff' }} /> : 
                        <EyeInvisibleOutlined style={{ color: '#999999' }} />
                    }
                  />
                </Form.Item>

                <Form.Item 
                  name="confirmPassword" 
                  rules={[
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'));
                      },
                    }),
                  ]}
                > 
                  <Input.Password 
                    prefix={<LockOutlined style={{ color: '#1890ff' }} />} 
                    placeholder="确认密码"
                    style={{
                        borderRadius: 8,
                        background: isDarkMode ? '#262626' : '#fafafa',
                        border: isDarkMode ? '#434343' : '#f0f0f0'
                    }}
                    iconRender={(visible) => 
                      visible ? 
                        <EyeOutlined style={{ color: '#1890ff' }} /> : 
                        <EyeInvisibleOutlined style={{ color: '#999999' }} />
                    }
                  />
                </Form.Item>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    className="register-form-button"
                    loading={loading}
                    style={{ 
                      width: '100%',
                      height: '40px',
                      fontSize: '16px',
                      borderRadius: '8px',
                    }}
                  >
                    {loading ? '注册中...' : '注册账户'}
                  </Button>
                </Form.Item>
              </Form>

              {/* 登录入口 */}
              <div className="login-link-section">
                <Text style={{ 
                  color: isDarkMode ? '#cccccc' : '#666666',
                  fontSize: 14
                }}>
                  已有账户？
                </Text>
                <Button 
                  type="link" 
                  style={{ 
                    padding: '0 8px',
                    color: '#1890ff',
                    fontSize: 14,
                    fontWeight: 500
                  }}
                  onClick={() => redirectToLogin({
                    message: '切换到登录页面',
                    clearAuth: false,
                  })}
                >
                  立即登录
                </Button>
              </div>

              <div className="other-register-section">
                <Divider style={{ 
                  margin: '24px 0',
                  borderColor: isDarkMode ? '#434343' : '#f0f0f0'
                }}>
                  <Text style={{ 
                    color: isDarkMode ? '#cccccc' : '#999999',
                    fontSize: 12
                  }}>
                    其他注册方式
                  </Text>
                </Divider>

                <Space style={{ width: '100%', justifyContent: 'center' }}>
                  <Button 
                    shape="circle" 
                    size="large"
                    style={{ 
                      width: 48,
                      height: 48,
                      border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
                      background: isDarkMode ? '#262626' : '#fafafa'
                    }}
                  >
                    <TeamOutlined style={{ color: '#1890ff' }} />
                  </Button>
                  <Button 
                    shape="circle" 
                    size="large"
                    style={{ 
                      width: 48,
                      height: 48,
                      border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
                      background: isDarkMode ? '#262626' : '#fafafa'
                    }}
                  >
                    <SafetyOutlined style={{ color: '#52c41a' }} />
                  </Button>
                  <Button 
                    shape="circle" 
                    size="large"
                    style={{ 
                      width: 48,
                      height: 48,
                      border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
                      background: isDarkMode ? '#262626' : '#fafafa'
                    }}
                  >
                    <SettingOutlined style={{ color: '#722ed1' }} />
                  </Button>
                </Space>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* 主题切换按钮 */}
      <div className="theme-toggle">
        <Button
          type="text"
          icon={isDarkMode ? <TeamOutlined /> : <LockOutlined />}
          onClick={toggleTheme}
          style={{
            width: 48,
            height: 48,
            borderRadius: '50%',
            background: isDarkMode ? '#262626' : '#ffffff',
            border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            color: isDarkMode ? '#ffffff' : '#666666'
          }}
        />
      </div>
    </div>
  );
};

export default RegisterPage;

