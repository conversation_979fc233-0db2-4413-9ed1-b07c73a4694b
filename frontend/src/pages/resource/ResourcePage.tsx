import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Card,
  Row,
  Col,
  Typography,
  Tree,
  TreeSelect,
  Switch,
  Tag,
  Badge,
  Avatar,
  Empty,
  Tabs,
  Layout,
  Spin,
  List,
  Popconfirm,
  InputNumber,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SettingOutlined,
  ApiOutlined,
  FolderOutlined,
  FileOutlined,
  KeyOutlined,
  TeamOutlined,
  SaveOutlined,
  InfoCircleOutlined,
  AppstoreOutlined,
  SecurityScanOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import {
  getResources,
  getResourceTree,
  createResource,
  updateResource,
  deleteResource,
  getResourcePermissions,
  getAvailableAPIResources,
  batchAssignAPIResources,
  configureResourcePermissions,
  assignResourcesToApp,
  type Resource,
  type ResourceTreeNode,

} from '../../services/resource';
import {
  getApplications,
  type Application,
} from '../../services/application';
import { TenantService } from '../../services/tenant';
import type { Tenant } from '../../types/tenant';
import { useAuth } from '../../contexts/AuthContext';
import {
  createPermission,
  deletePermission,
  type Permission as PermissionType,
  type CreatePermissionItem,
} from '../../services/permission';
import { showAPIError, applyFieldErrorsToForm } from '../../utils/errorHandler';
import { showError, showSuccess } from '../../utils/messageManager';
import { SUCCESS, VALIDATION_ERROR } from '../../constants/errorCodes';

const { Option } = Select;
const { Text, Title } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Sider, Content } = Layout;

// 资源类型选项 - 移除API类型，API资源将在右侧独立管理
const resourceTypes = [
  { value: 'menu', label: '菜单', icon: <FolderOutlined />, color: '#1890ff' },
  { value: 'page', label: '页面', icon: <FileOutlined />, color: '#52c41a' },
  { value: 'button', label: '按钮', icon: <SettingOutlined />, color: '#fa8c16' },
  { value: 'component', label: '组件', icon: <AppstoreOutlined />, color: '#13c2c2' },
];

// 权限范围选项
const permissionScopes = [
  { value: 'all', label: '全部权限' },
  { value: 'self', label: '普通权限' },
];

// 租户和应用选择组件
interface TenantAppSelectorProps {
  selectedTenantId: number | null;
  selectedAppId: string | null;
  selectedInternalAppId: number | null;
  onTenantChange: (tenantId: number | null) => void;
  onAppChange: (appId: string | null, internalAppId: number | null) => void;
}

const TenantAppSelector: React.FC<TenantAppSelectorProps> = ({
  selectedTenantId,
  selectedAppId,
  selectedInternalAppId,
  onTenantChange,
  onAppChange,
}) => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [apps, setApps] = useState<Application[]>([]);
  const [loadingTenants, setLoadingTenants] = useState(false);
  const [loadingApps, setLoadingApps] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // 加载租户列表
  const loadTenants = useCallback(async () => {
    setLoadingTenants(true);
    setError(null);
    try {
      const response = await TenantService.listTenants();
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        setError(`加载租户失败: ${response.message}`);
        return;
      }

      // 处理成功响应
      setTenants(response.data || []);
    } catch (error) {
      console.error('加载租户失败:', error);
      setError('加载租户失败，请稍后重试');
    } finally {
      setLoadingTenants(false);
    }
  }, []);

  // 加载应用列表
  const loadApps = useCallback(async () => {
    setLoadingApps(true);
    setError(null);
    try {
      // 构建请求参数
      const params: any = {};
      if (selectedTenantId) {
        // 如果选择了租户，传递tenant_id参数
        params.tenant_id = selectedTenantId;
      } else {
        // 否则不传递tenant_id参数，使用当前用户上下文
      }

      const response = await getApplications(params);
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        setError(`加载应用失败: ${response.message}`);
        setApps([]);
        return;
      }

      // 处理成功响应
      setApps(response.data || []);
      // 如果只有一个应用，自动选择
      if (response.data && response.data.length === 1 && !selectedAppId) {
        const app = response.data[0];
        onAppChange(app.app_name, app.internal_app_id);
      }
    } catch (error) {
      console.error('加载应用失败:', error);
      setError('加载应用失败，请稍后重试');
      setApps([]);
    } finally {
      setLoadingApps(false);
    }
  }, [selectedTenantId, selectedAppId, onAppChange]);

  // 统一的应用加载逻辑 - 只在租户选择后触发
  useEffect(() => {
    const loadData = async () => {
      // 只有选择了租户才加载应用
      if (!selectedTenantId) {
        setApps([]);
        return;
      }

      // 防止重复请求
      if (loadingApps) {
        return;
      }

      setLoadingApps(true);
      setError(null);
      try {
        // 构建请求参数 - 始终传递选择的租户ID
        const params: any = {
          tenant_id: selectedTenantId
        };

        const response = await getApplications(params);
        
        // 处理业务错误 - 检查 code 字段
        if (response.code !== SUCCESS) {
          setError(`加载应用失败: ${response.message}`);
          setApps([]);
          return;
        }

        // 处理成功响应
        setApps(response.data || []);
        // 如果只有一个应用，自动选择
        if (response.data && response.data.length === 1 && !selectedAppId) {
          const app = response.data[0];
          onAppChange(app.app_name, app.internal_app_id);
        }
      } catch (error) {
        console.error('加载应用失败:', error);
        setError('加载应用失败，请稍后重试');
        setApps([]);
      } finally {
        setLoadingApps(false);
      }
    };

    loadData();
  }, [selectedTenantId]); // 只依赖 selectedTenantId

  const handleTenantChange = useCallback((tenantId: number) => {
    onTenantChange(tenantId);
    onAppChange(null, null); // 清空应用选择
  }, [onTenantChange, onAppChange]);

  const handleAppChange = useCallback((appId: string) => {
    const app = apps.find(a => a.app_name === appId);
    onAppChange(appId, app ? app.internal_app_id : null);
  }, [apps, onAppChange]);

  return (
    <Card 
      size="small"
      style={{ marginBottom: 16 }}
    >
      {error && (
        <div style={{ marginBottom: 16, color: '#ff4d4f' }}>
          <Text type="danger">{error}</Text>
        </div>
      )}
      
      <Row gutter={16} align="middle">
        {/* 租户选择器 - 始终显示 */}
        <Col span={6}>
          <Form.Item label="选择租户" style={{ marginBottom: 0 }}>
            <Select
              placeholder="请选择租户"
              value={selectedTenantId}
              onChange={handleTenantChange}
              loading={loadingTenants}
              allowClear
              showSearch
              optionFilterProp="children"
              onFocus={loadTenants}
            >
              {tenants.map(tenant => (
                <Option key={tenant.id} value={tenant.id}>
                  {tenant.tenant_name} ({tenant.tenant_code})
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        
        {/* 应用选择器 - 只有选择租户后才显示 */}
        {selectedTenantId && (
          <Col span={6}>
            <Form.Item label="选择应用" style={{ marginBottom: 0 }}>
              <Select
                placeholder="请选择应用"
                value={selectedAppId}
                onChange={handleAppChange}
                loading={loadingApps}
                allowClear
                showSearch
                optionFilterProp="children"
              >
                {apps.map(app => (
                  <Option key={app.internal_app_id} value={app.app_name}>
                    {app.app_name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        )}

        {/* 当前租户信息显示 */}
        {selectedTenantId && (
          <Col span={4}>
            <Form.Item label="当前租户" style={{ marginBottom: 0 }}>
              <Text type="secondary">
                ID: {selectedTenantId}
              </Text>
            </Form.Item>
          </Col>
        )}

        {/* 刷新按钮 */}
        <Col span={2}>
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={loadApps}
            loading={loadingApps}
            title="刷新应用列表"
            disabled={!selectedTenantId}
          />
        </Col>
      </Row>
    </Card>
  );
};

// 转换资源树数据
const convertToTreeData = (resources: ResourceTreeNode[]): any[] => {
  if (!Array.isArray(resources)) return [];
  
  return resources.map(resource => ({
    title: (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        {resourceTypes.find(t => t.value === resource.resource_type)?.icon}
        <span>{resource.display_name}</span>
        <Tag color={resourceTypes.find(t => t.value === resource.resource_type)?.color}>
          {resourceTypes.find(t => t.value === resource.resource_type)?.label}
        </Tag>
        {resource.is_universal && <Tag color="green">通用</Tag>}
        {resource.is_system && <Tag color="red">系统</Tag>}
        {resource.children_count > 0 && (
          <Badge count={resource.children_count} size="small" />
        )}
      </div>
    ),
    key: resource.id,
    value: resource.id,
    data: resource,
    children: resource.children ? convertToTreeData(resource.children) : undefined,
  }));
};

const ResourcePage: React.FC = () => {
  const { user } = useAuth();
  
  // 租户和应用选择状态
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [selectedAppId, setSelectedAppId] = useState<string | null>(null);
  const [selectedInternalAppId, setSelectedInternalAppId] = useState<number | null>(null);
  
  // 树形数据相关状态
  const [treeData, setTreeData] = useState<any[]>([]);
  const [selectedTreeKeys, setSelectedTreeKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedResource, setSelectedResource] = useState<Resource | null>(null);
  
  // 加载状态
  const [treeLoading, setTreeLoading] = useState(false);
  const [tabLoading, setTabLoading] = useState(false);
  
  // Tab相关状态
  const [activeTab, setActiveTab] = useState('basic');
  
  // 基本信息Tab状态
  const [basicForm] = Form.useForm();
  const [isEditingBasic, setIsEditingBasic] = useState(false);
  
  // 功能列表Tab状态
  const [apiList, setApiList] = useState<{ id?: number; name: string; path: string }[]>([]);
  const [apiTabLoading, setApiTabLoading] = useState(false);
  const [availableAPIs, setAvailableAPIs] = useState<Resource[]>([]);
  const [assignedAPIIds, setAssignedAPIIds] = useState<number[]>([]);
  const [apiSelectionModalVisible, setApiSelectionModalVisible] = useState(false);
  const [selectedAPIIds, setSelectedAPIIds] = useState<number[]>([]);
  
  // 权限配置Tab状态
  const [resourcePermissions, setResourcePermissions] = useState<PermissionType[]>([]);
  const [permissionForm] = Form.useForm();
  const [isAddingPermission, setIsAddingPermission] = useState(false);
  const [isBatchAddingPermissions, setIsBatchAddingPermissions] = useState(false);
  const [batchPermissions, setBatchPermissions] = useState<CreatePermissionItem[]>([]);

  // 新增资源状态
  const [isAddingResource, setIsAddingResource] = useState(false);
  const [addResourceForm] = Form.useForm();
  
  // 资源分配状态
  const [isAssigningResources, setIsAssigningResources] = useState(false);
  const [selectedResourcesForAssignment, setSelectedResourcesForAssignment] = useState<React.Key[]>([]);
  const [assignmentModalVisible, setAssignmentModalVisible] = useState(false);
  const [targetApps, setTargetApps] = useState<Application[]>([]);
  const [loadingTargetApps, setLoadingTargetApps] = useState(false);
  const [selectedTargetAppId, setSelectedTargetAppId] = useState<number | null>(null);
  
  // 分配模态框租户选择状态
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loadingTenants, setLoadingTenants] = useState(false);
  const [selectedTargetTenantId, setSelectedTargetTenantId] = useState<number | null>(null);

  // 渲染基本信息Tab
  const renderBasicInfoTab = () => {
    if (!selectedResource) {
      return (
        <Empty 
          description="请选择一个资源查看详细信息"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <Card 
        title="基本信息" 
        extra={
          <Space>
            {isEditingBasic ? (
              <>
                <Button onClick={() => setIsEditingBasic(false)}>取消</Button>
                <Button type="primary" icon={<SaveOutlined />} onClick={handleSaveBasicInfo}>
                  保存
                </Button>
              </>
            ) : (
              <Button type="primary" icon={<EditOutlined />} onClick={() => setIsEditingBasic(true)}>
                编辑
              </Button>
            )}
          </Space>
        }
      >
        <Spin spinning={tabLoading}>
          <Form
            form={basicForm}
            layout="vertical"
            disabled={!isEditingBasic}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="资源名称"
                  name="name"
                  rules={[
                    { required: true, message: '请输入资源名称' },
                    { min: 3, max: 50, message: '资源名称必须是3-50个字符' },
                    { 
                      pattern: /^[a-zA-Z0-9_\-\u4e00-\u9fa5]{3,50}$/,
                      message: '资源名称只能包含字母、数字、下划线、中划线或中文'
                    }
                  ]}
                >
                  <Input placeholder="请输入资源名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="显示名称"
                  name="display_name"
                  rules={[
                    { required: true, message: '请输入显示名称' },
                    { min: 2, max: 100, message: '显示名称必须是2-100个字符' }
                  ]}
                >
                  <Input placeholder="请输入显示名称" />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="资源类型"
                  name="resource_type"
                  rules={[{ required: true, message: '请选择资源类型' }]}
                >
                  <Select placeholder="请选择资源类型">
                    {resourceTypes.map(type => (
                      <Option key={type.value} value={type.value}>
                        {type.icon} {type.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="父节点"
                  name="parent_id"
                >
                  <TreeSelect
                    placeholder="请选择父节点"
                    allowClear
                    treeData={treeData}
                    treeDefaultExpandAll
                    showSearch
                    treeNodeFilterProp="title"
                  />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="路径"
                  name="path"
                  rules={[
                    {
                      pattern: /^\/.*$/,
                      message: '路径必须以 / 开头'
                    }
                  ]}
                >
                  <Input placeholder="请输入路径（必须以 / 开头）" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="图标"
                  name="icon"
                >
                  <Input placeholder="请输入图标" />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="排序"
                  name="sort_order"
                >
                  <InputNumber min={0} placeholder="排序" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="公开访问"
                  name="is_public"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="可分配"
                  name="assignable"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item
              label="描述"
              name="description"
              rules={[
                { max: 255, message: '描述不能超过255个字符' }
              ]}
            >
              <TextArea rows={3} placeholder="请输入描述" />
            </Form.Item>
          </Form>
        </Spin>
      </Card>
    );
  };

  // 渲染功能列表Tab
  const renderApiListTab = () => {
    if (!selectedResource) {
      return (
        <Empty 
          description="请选择一个资源配置API功能"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }
    return (
      <Card
        title="API功能列表"
        extra={
          <Space>
            <Button 
              icon={<PlusOutlined />} 
              onClick={handleOpenApiSelection}
              disabled={availableAPIs.length === 0}
            >
              选择API
            </Button>
            <Button type="primary" onClick={handleSaveApis} loading={apiTabLoading}>保存</Button>
          </Space>
        }
      >
        <Spin spinning={apiTabLoading}>
          {apiList.length === 0 ? (
            <Empty 
              description="暂无API功能，点击'选择API'添加"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            <Table
              dataSource={apiList}
              rowKey={(row, idx) => (row.id ? String(row.id) : `new-${idx}`)}
              pagination={false}
              size="small"
              columns={[
                {
                  title: 'API名称',
                  dataIndex: 'name',
                  render: (text, record) => (
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <ApiOutlined style={{ color: '#722ed1' }} />
                      <span>{text}</span>
                    </div>
                  ),
                },
                {
                  title: 'API路径',
                  dataIndex: 'path',
                  render: (text) => (
                    <Text code>{text}</Text>
                  ),
                },
                {
                  title: '操作',
                  render: (_, record, idx) => (
                    <Button 
                      danger 
                      onClick={() => handleRemoveApi(idx)} 
                      icon={<DeleteOutlined />}
                      size="small"
                    >
                      移除
                    </Button>
                  ),
                },
              ]}
            />
          )}
        </Spin>

        {/* API选择模态框 */}
        <Modal
          title="选择API"
          open={apiSelectionModalVisible}
          onOk={handleConfirmApiSelection}
          onCancel={() => {
            setApiSelectionModalVisible(false);
            setSelectedAPIIds([]);
          }}
          width={800}
        >
          <div style={{ marginBottom: 16 }}>
            <Text>已选择 {selectedAPIIds.length} 个API</Text>
          </div>
          <Table
            dataSource={availableAPIs}
            rowKey="id"
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: selectedAPIIds,
              onChange: (selectedRowKeys) => {
                setSelectedAPIIds(selectedRowKeys as number[]);
              },
              getCheckboxProps: (record) => ({
                disabled: assignedAPIIds.includes(record.id),
              }),
            }}
            pagination={false}
            size="small"
            columns={[
              {
                title: 'API名称',
                dataIndex: 'display_name',
                render: (text, record) => (
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <ApiOutlined style={{ color: '#722ed1' }} />
                    <span>{text}</span>
                    {assignedAPIIds.includes(record.id) && (
                      <Tag color="green">已分配</Tag>
                    )}
                  </div>
                ),
              },
              {
                title: 'API路径',
                dataIndex: 'path',
                render: (text) => (
                  <Text code>{text}</Text>
                ),
              },
              {
                title: '服务名称',
                dataIndex: 'service_name',
                render: (text) => text ? <Tag color="blue">{text}</Tag> : '-',
              },
              {
                title: 'HTTP方法',
                dataIndex: 'api_method',
                render: (text) => text ? <Tag color="orange">{text}</Tag> : '-',
              },
            ]}
          />
        </Modal>
      </Card>
    );
  };

  // 渲染权限配置Tab
  const renderPermissionConfigTab = () => {
    if (!selectedResource) {
      return (
        <Empty 
          description="请选择一个资源配置权限"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <Card 
        title="权限配置" 
        extra={
          <Space>
            <Button 
              type="default" 
              icon={<PlusOutlined />} 
              onClick={() => setIsAddingPermission(true)}
            >
              添加权限
            </Button>
            <Button 
              type="primary" 
              icon={<TeamOutlined />} 
              onClick={() => {
                setIsBatchAddingPermissions(true);
                setBatchPermissions([]);
              }}
            >
              批量添加
            </Button>
          </Space>
        }
      >
        <Spin spinning={tabLoading}>
          <List
            dataSource={resourcePermissions}
            renderItem={permission => (
              <List.Item
                actions={[
                  <Popconfirm
                    title="确定删除此权限吗？"
                    onConfirm={() => handleDeletePermission(permission.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button type="text" danger icon={<DeleteOutlined />}>
                      删除
                    </Button>
                  </Popconfirm>
                ]}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<KeyOutlined />} />}
                  title={permission.display_name}
                                     description={
                     <Space direction="vertical" size="small">
                       <Text>权限名: {permission.name}</Text>
                       <Text>范围: {permission.scope_display_name || permission.scope}</Text>
                       <Text type="secondary">{permission.description}</Text>
                     </Space>
                   }
                />
                <Tag color={permission.status === 'active' ? 'green' : 'red'}>
                  {permission.status === 'active' ? '激活' : '禁用'}
                </Tag>
              </List.Item>
            )}
          />
        </Spin>
        
        {/* 添加权限弹窗 */}
        <Modal
          title="添加权限"
          open={isAddingPermission}
          onOk={handleAddPermission}
          onCancel={() => {
            setIsAddingPermission(false);
            permissionForm.resetFields();
          }}
          width={600}
        >
          <Form
            form={permissionForm}
            layout="vertical"
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="权限名称"
                  name="name"
                  rules={[{ required: true, message: '请输入权限名称' }]}
                >
                  <Input placeholder="请输入权限名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="权限码"
                  name="code"
                  rules={[{ required: true, message: '请输入权限码' }]}
                >
                  <Input placeholder="请输入权限码" />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="显示名称"
                  name="display_name"
                  rules={[
                    { required: true, message: '请输入显示名称' },
                    { min: 2, max: 100, message: '显示名称必须是2-100个字符' }
                  ]}
                >
                  <Input placeholder="请输入显示名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="权限范围"
                  name="scope"
                  rules={[{ required: true, message: '请选择权限范围' }]}
                >
                  <Select placeholder="请选择权限范围">
                    {permissionScopes.map(scope => (
                      <Option key={scope.value} value={scope.value}>
                        {scope.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item
              label="权限描述"
              name="description"
            >
              <TextArea rows={3} placeholder="请输入权限描述" />
            </Form.Item>
          </Form>
        </Modal>

        {/* 批量添加权限弹窗 */}
        <Modal
          title="批量添加权限"
          open={isBatchAddingPermissions}
          onOk={handleBatchAddPermissions}
          onCancel={() => {
            setIsBatchAddingPermissions(false);
            setBatchPermissions([]);
          }}
          width={900}
          okText="批量添加"
          cancelText="取消"
          okButtonProps={{ disabled: batchPermissions.length === 0 }}
        >
          <div style={{ marginBottom: 16 }}>
            <Button 
              type="dashed" 
              icon={<PlusOutlined />} 
              onClick={handleAddToBatch}
              block
            >
              添加权限项
            </Button>
          </div>
          
          <div style={{ maxHeight: 400, overflow: 'auto' }}>
            {batchPermissions.map((permission, index) => (
              <Card 
                key={index} 
                size="small" 
                style={{ marginBottom: 8 }}
                extra={
                  <Button 
                    type="text" 
                    danger 
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveBatchPermission(index)}
                  >
                    删除
                  </Button>
                }
              >
                <Row gutter={8}>
                  <Col span={6}>
                    <Input
                      placeholder="权限名称"
                      value={permission.name}
                      onChange={(e) => handleUpdateBatchPermission(index, 'name', e.target.value)}
                    />
                  </Col>
                  <Col span={6}>
                    <Input
                      placeholder="权限码"
                      value={permission.code}
                      onChange={(e) => handleUpdateBatchPermission(index, 'code', e.target.value)}
                    />
                  </Col>
                  <Col span={6}>
                    <Input
                      placeholder="显示名称"
                      value={permission.display_name}
                      onChange={(e) => handleUpdateBatchPermission(index, 'display_name', e.target.value)}
                    />
                  </Col>
                  <Col span={6}>
                    <Select
                      placeholder="权限范围"
                      value={permission.scope}
                      onChange={(value) => handleUpdateBatchPermission(index, 'scope', value)}
                      style={{ width: '100%' }}
                    >
                      {permissionScopes.map(scope => (
                        <Option key={scope.value} value={scope.value}>
                          {scope.label}
                        </Option>
                      ))}
                    </Select>
                  </Col>
                </Row>
                <Row style={{ marginTop: 8 }}>
                  <Col span={24}>
                    <Input.TextArea
                      placeholder="权限描述"
                      value={permission.description}
                      onChange={(e) => handleUpdateBatchPermission(index, 'description', e.target.value)}
                      rows={2}
                    />
                  </Col>
                </Row>
              </Card>
            ))}
            
            {batchPermissions.length === 0 && (
              <Empty 
                description="暂无权限项，点击上方按钮添加"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </div>
        </Modal>
      </Card>
    );
  };

  // 加载树形数据
  const loadTreeData = useCallback(async () => {
    if (!selectedInternalAppId) {
      console.warn('未选择内部应用ID，跳过资源树加载');
      return;
    }
    
    setTreeLoading(true);
    try {
      const params: any = {
        include_universal: true,
      };
      
      // 传递租户ID
      if (selectedTenantId) {
        params.tenant_id = selectedTenantId;
      }
      
      // 如果选择了应用，传递 internal_app_id
      if (selectedInternalAppId) {
        params.internal_app_id = selectedInternalAppId;
      }
      
      const treeRes = await getResourceTree(params);
      
      // 处理业务错误 - 检查 code 字段
      if (treeRes.code !== SUCCESS) {
        console.warn('获取资源树失败:', treeRes.message);
        setTreeData([]);
        return;
      }

      // 处理成功响应
      const convertedData = convertToTreeData(treeRes.data || []);
      setTreeData(convertedData);
      
      // 自动展开根节点
      if (convertedData.length > 0) {
        const rootKeys = convertedData.map((item: any) => item.key);
        setExpandedKeys(rootKeys);
      }
    } catch (error) {
      console.error('加载资源树出错:', error);
      showAPIError(error);
    } finally {
      setTreeLoading(false);
    }
  }, [selectedInternalAppId, selectedTenantId]);

  // 加载选中资源的详细信息
  const loadResourceDetails = useCallback(async (resourceId: number) => {
    if (!resourceId) return;
    
    setTabLoading(true);
    try {
      // 从树形数据中找到选中的资源
      const findResourceInTree = (nodes: any[], targetId: number): Resource | null => {
        for (const node of nodes) {
          if (node.key === targetId) {
            return node.data;
          }
          if (node.children) {
            const found = findResourceInTree(node.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };
      
      const resource = findResourceInTree(treeData, resourceId);
      if (resource) {
        setSelectedResource(resource);
        
        // 设置基本信息表单数据
        basicForm.setFieldsValue({
          name: resource.name,
          display_name: resource.display_name,
          description: resource.description,
          resource_type: resource.resource_type,
          parent_id: resource.parent_id,
          path: resource.path,
          icon: resource.icon,
          sort_order: resource.sort_order,
          is_public: resource.is_public,
          public_level: resource.public_level,
          assignable: resource.assignable,
        });
        
        // 如果是页面、按钮或组件类型，加载API资源
        if (resource.resource_type === 'page' || resource.resource_type === 'button' || resource.resource_type === 'component') {
          await loadApiList(resource.id);
        }
        
        // 加载权限配置
        await loadResourcePermissions(resourceId);
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setTabLoading(false);
    }
  }, [basicForm, treeData]);

  // 加载API列表
  const loadApiList = useCallback(async (resourceId: number) => {
    if (!resourceId) return;
    
    setApiTabLoading(true);
    try {
      // 获取已分配的API资源
      const availableRes = await getAvailableAPIResources(resourceId);
      
      // 处理业务错误 - 检查 code 字段
      if (availableRes.code !== SUCCESS) {
        console.warn('获取API资源失败:', availableRes.message);
        setApiList([]);
        setAssignedAPIIds([]);
        return;
      }

      // 处理成功响应
      const assignedResources = availableRes.data?.data?.assigned_resources || [];
      setApiList(assignedResources.map((api: Resource) => ({
        id: api.id,
        name: api.display_name,
        path: api.path || '',
      })));
      setAssignedAPIIds(assignedResources.map((api: Resource) => api.id));
    } catch (error) {
      showAPIError(error);
      setApiList([]);
      setAssignedAPIIds([]);
    } finally {
      setApiTabLoading(false);
    }
  }, []);



  // 保存API（现在通过批量分配API来处理）
  const handleSaveApis = async () => {
    message.info('API资源已通过选择API功能进行管理');
  };

  // 加载资源权限
  const loadResourcePermissions = useCallback(async (resourceId: number) => {
    try {
      const permRes = await getResourcePermissions(resourceId);
      
      // 处理业务错误 - 检查 code 字段
      if (permRes.code !== SUCCESS) {
        console.warn('获取资源权限失败:', permRes.message);
        setResourcePermissions([]);
        return;
      }

      // 处理成功响应
      // 转换resource服务返回的Permission为permission服务使用的Permission类型
      const permissions = (permRes.data?.permissions || []).map((p: any) => ({
        ...p,
        resource_type: 'resource',
        scope: p.scope || 'self',
        scope_display_name: p.scope_display_name || (p.scope === 'all' ? '全部权限' : '普通权限'),
      }));
      setResourcePermissions(permissions as PermissionType[]);
    } catch (error) {
      showAPIError(error);
    }
  }, []);

  // 首次加载 - 移除自动加载逻辑，只在选择应用后加载
  // useEffect(() => {
  //   if ((user?.tenant_id && selectedInternalAppId) ||
  //       (selectedTenantId && selectedInternalAppId)) {
  //     loadTreeData();
  //   }
  // }, [loadTreeData, selectedTenantId, selectedInternalAppId, user?.tenant_id]);

  // 资源分配功能 - 打开分配模态框
  const handleOpenAssignmentModal = () => {
    if (selectedResourcesForAssignment.length === 0) {
      message.warning('请先选择要分配的资源');
      return;
    }
    setAssignmentModalVisible(true);
    // 重置状态
    setSelectedTargetTenantId(null);
    setSelectedTargetAppId(null);
    setTargetApps([]);
    // 加载租户列表
    loadTenants();
  };

  // 加载租户列表
  const loadTenants = async () => {
    setLoadingTenants(true);
    try {
      const response = await TenantService.listTenants({});
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || '获取租户列表失败');
        setTenants([]);
        return;
      }

      // 处理成功响应
      setTenants(response.data || []);
    } catch (error) {
      showAPIError(error);
      setTenants([]);
    } finally {
      setLoadingTenants(false);
    }
  };

  // 加载目标应用列表
  const loadTargetApps = async (tenantId?: number) => {
    setLoadingTargetApps(true);
    try {
      const params: any = {};
      // 如果指定了租户ID，使用指定的租户ID
      const targetTenantId = tenantId ?? selectedTargetTenantId;
      if (targetTenantId) {
        params.tenant_id = targetTenantId;
      }
      
      const response = await getApplications(params);
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || '获取应用列表失败');
        setTargetApps([]);
        return;
      }

      // 处理成功响应
      // 过滤掉当前选择的应用
      const filteredApps = (response.data || []).filter(app => 
        app.internal_app_id !== selectedInternalAppId
      );
      setTargetApps(filteredApps);
    } catch (error) {
      showAPIError(error);
      setTargetApps([]);
    } finally {
      setLoadingTargetApps(false);
    }
  };

  // 处理租户选择变化
  const handleTargetTenantChange = (tenantId: number) => {
    setSelectedTargetTenantId(tenantId);
    setSelectedTargetAppId(null); // 清空应用选择
    setTargetApps([]); // 清空应用列表
    if (tenantId) {
      loadTargetApps(tenantId); // 加载新租户的应用列表
    }
  };

  // 确认分配资源到目标应用
  const handleAssignResources = async () => {
    if (!selectedTargetAppId || selectedResourcesForAssignment.length === 0) {
      message.warning('请选择目标应用和要分配的资源');
      return;
    }

    try {
      const resourceIds = selectedResourcesForAssignment.map(key => Number(key));
      const targetApp = targetApps.find(app => app.internal_app_id === selectedTargetAppId);
      
      if (!targetApp) {
        showError('目标应用不存在');
        return;
      }

      const response = await assignResourcesToApp({
        resource_ids: resourceIds,
        app_name: targetApp.app_name,
        tenant_id: selectedTenantId || user?.tenant_id,
        target_internal_app_id: selectedTargetAppId, // 新增目标应用ID参数
      });

      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || '资源分配失败', 'resource-assign-error');
        return;
      }

      // 处理成功响应
      showSuccess(`成功分配 ${resourceIds.length} 个资源给应用 ${targetApp.app_name}`);
      setAssignmentModalVisible(false);
      setIsAssigningResources(false);
      setSelectedResourcesForAssignment([]);
      setSelectedTargetAppId(null);
    } catch (error) {
      showAPIError(error);
    }
  };

  // 处理租户变更
  const handleTenantChange = (tenantId: number | null) => {
    setSelectedTenantId(tenantId);
    setTreeData([]);
    setSelectedResource(null);
    setSelectedTreeKeys([]);
  };

  // 处理应用变更
  const handleAppChange = (appId: string | null, internalAppId: number | null) => {
    setSelectedAppId(appId);
    setSelectedInternalAppId(internalAppId);
    
    // 清空之前的数据
    setTreeData([]);
    setSelectedResource(null);
    setSelectedTreeKeys([]);
    setExpandedKeys([]);
  };

  // 当选择的应用发生变化时自动加载资源树
  useEffect(() => {
    if (selectedInternalAppId) {
      loadTreeData();
    } else {
      setTreeData([]);
    }
  }, [selectedInternalAppId, loadTreeData]);

  // 树节点选择处理
  const handleTreeSelect = (selectedKeys: React.Key[]) => {
    setSelectedTreeKeys(selectedKeys);
    if (selectedKeys.length > 0) {
      const resourceId = selectedKeys[0] as number;
      loadResourceDetails(resourceId);
    } else {
      setSelectedResource(null);
      setIsEditingBasic(false);
      // 清空API列表
      setApiList([]);
      setAssignedAPIIds([]);
    }
  };

  // 树节点展开处理
  const handleTreeExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };

  // 保存基本信息
  const handleSaveBasicInfo = async () => {
    if (!selectedResource) return;
    
    try {
      const values = await basicForm.validateFields();
      const response = await updateResource({
        id: selectedResource.id,
        ...values,
      });
      
      // 处理验证错误 - 检查 errors 字段
      if (response.errors && response.errors.length > 0) {
        applyFieldErrorsToForm(response.errors, basicForm);
        return;
      }

      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || '资源信息更新失败', 'resource-update-error');
        return;
      }

      // 处理成功响应
      showSuccess('资源信息更新成功');
      setIsEditingBasic(false);
      
      // 重新加载树形数据
      await loadTreeData();
    } catch (error) {
      showAPIError(error);
    }
  };

  // 添加单个权限
  const handleAddPermission = async () => {
    if (!selectedResource) return;
    
    try {
      const values = await permissionForm.validateFields();
      const response = await createPermission({
        ...values,
        resource_id: selectedResource.id,
      });
      
      // 处理验证错误 - 检查 errors 字段
      if (response.errors && response.errors.length > 0) {
        applyFieldErrorsToForm(response.errors, permissionForm);
        return;
      }

      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || '权限创建失败', 'permission-create-error');
        return;
      }

      // 处理成功响应
      showSuccess('权限创建成功');
      setIsAddingPermission(false);
      permissionForm.resetFields();
      await loadResourcePermissions(selectedResource.id);
    } catch (error) {
      showAPIError(error);
    }
  };

  // 批量添加权限
  const handleBatchAddPermissions = async () => {
    if (!selectedResource || batchPermissions.length === 0) return;
    
    try {
      const result = await configureResourcePermissions({
        permissions: batchPermissions,
      });
      
      const { success_count, skipped_count, total_count } = result.data;
      
      if (success_count > 0) {
        showSuccess(`成功添加 ${success_count} 个权限${skipped_count > 0 ? `，跳过 ${skipped_count} 个重复权限` : ''}`);
      } else if (skipped_count === total_count) {
        message.warning('所有权限都已存在，未添加新权限');
      }
      
      setIsBatchAddingPermissions(false);
      setBatchPermissions([]);
      await loadResourcePermissions(selectedResource.id);
    } catch (error) {
      showAPIError(error);
    }
  };

  // 添加权限到批量列表
  const handleAddToBatch = () => {
    const newPermission: CreatePermissionItem = {
      name: '',
      code: '',
      display_name: '',
      action: 'read', // 默认操作类型
      scope: 'self',
      description: '',
    };
    setBatchPermissions([...batchPermissions, newPermission]);
  };

  // 更新批量权限项
  const handleUpdateBatchPermission = (index: number, field: keyof CreatePermissionItem, value: string) => {
    const updated = [...batchPermissions];
    updated[index] = { ...updated[index], [field]: value };
    setBatchPermissions(updated);
  };

  // 删除批量权限项
  const handleRemoveBatchPermission = (index: number) => {
    const updated = batchPermissions.filter((_, i) => i !== index);
    setBatchPermissions(updated);
  };

  // 删除权限
  const handleDeletePermission = async (permissionId: number) => {
    try {
      const response = await deletePermission(permissionId);
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || '权限删除失败', 'permission-delete-error');
        return;
      }

      // 处理成功响应
      showSuccess('权限删除成功');
      if (selectedResource) {
        await loadResourcePermissions(selectedResource.id);
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  // 切换树节点时自动切换Tab
  useEffect(() => {
    if (selectedResource?.resource_type === 'api' && activeTab === 'api') {
      setActiveTab('basic');
    }
  }, [selectedResource, activeTab]);

  // 新增资源
  const handleAddResource = async () => {
    // 验证是否已选择租户和应用
    if (!selectedTenantId || !selectedInternalAppId) {
      showError('请先选择租户和应用', 'tenant-app-not-selected');
      return;
    }

    // 验证必填字段
    if (!addResourceForm.getFieldValue('name') || !addResourceForm.getFieldValue('display_name') || !addResourceForm.getFieldValue('resource_type')) {
      showError('请填写完整的新增资源信息', 'resource-form-validation-error');
      return;
    }

    try {
      const values = await addResourceForm.validateFields();
      
      // 使用页面顶部已选择的租户ID和应用ID
      const resourceData = {
        ...values,
        tenant_id: selectedTenantId,
        internal_app_id: selectedInternalAppId
      };
      
      const response = await createResource(resourceData);
      
      // 处理验证错误 - 检查 errors 字段
      if (response.errors && response.errors.length > 0) {
        applyFieldErrorsToForm(response.errors, addResourceForm);
        return;
      }

      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || '资源添加失败', 'resource-create-error');
        return;
      }

      // 处理成功响应
      showSuccess('资源添加成功');
      await loadTreeData();
      setIsAddingResource(false);
      addResourceForm.resetFields();
    } catch (error) {
      // 网络错误或其他异常
      showAPIError(error);
    }
  };

  // 打开API选择模态框
  const handleOpenApiSelection = async () => {
    if (!selectedResource) return;
    setApiSelectionModalVisible(true);
    try {
      const availableRes = await getAvailableAPIResources(selectedResource.id);
      
      // 处理业务错误 - 检查 code 字段
      if (availableRes.code !== SUCCESS) {
        showError(availableRes.message || '获取可用API资源失败', 'api-resources-error');
        setAvailableAPIs([]);
        return;
      }

      // 处理成功响应
      setAvailableAPIs(availableRes.data?.data?.available_resources || []);
      setAssignedAPIIds(apiList.map(api => api.id || 0)); // 已分配的API ID
    } catch (error) {
      showAPIError(error);
    }
  };

  // 确认API选择
  const handleConfirmApiSelection = async () => {
    if (!selectedResource || selectedAPIIds.length === 0) return;

    try {
      const response = await batchAssignAPIResources({
        page_resource_id: selectedResource.id,
        api_resource_ids: selectedAPIIds,
      });

      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || 'API资源分配失败', 'api-assign-error');
        return;
      }

      // 处理成功响应
      showSuccess('API资源分配成功');
      await loadApiList(selectedResource.id);
    } catch (error) {
      showAPIError(error);
    } finally {
      setApiSelectionModalVisible(false);
      setSelectedAPIIds([]);
    }
  };

  // 移除API
  const handleRemoveApi = async (index: number) => {
    if (!selectedResource) return;
    const apiToRemove = apiList[index];
    if (!apiToRemove.id) {
      setApiList(apiList.filter((_, i) => i !== index));
      return;
    }

    try {
      const response = await deleteResource(apiToRemove.id);
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        showError(response.message || 'API移除失败', 'api-remove-error');
        return;
      }

      // 处理成功响应
      showSuccess('API移除成功');
      await loadApiList(selectedResource.id);
    } catch (error) {
      showAPIError(error);
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 租户和应用选择组件 */}
      <TenantAppSelector
        selectedTenantId={selectedTenantId}
        selectedAppId={selectedAppId}
        selectedInternalAppId={selectedInternalAppId}
        onTenantChange={handleTenantChange}
        onAppChange={handleAppChange}
      />
      
      {/* 主要内容区域 */}
      <Layout style={{ flex: 1, minHeight: 0 }}>
        {/* 左侧树形结构 */}
        <Sider width={350} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column', padding: '16px' }}>
            {/* 只有选择了应用才显示操作按钮和树形结构 */}
            {selectedInternalAppId ? (
              <>
                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexShrink: 0 }}>
                  <Space>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => setIsAddingResource(true)}
                      size="small"
                    >
                      新增资源
                    </Button>
                    {selectedAppId && treeData.length > 0 && (
                      <Button
                        type="default"
                        icon={<SecurityScanOutlined />}
                        onClick={() => setIsAssigningResources(true)}
                        size="small"
                      >
                        分配资源
                      </Button>
                    )}
                    <Button 
                      type="text" 
                      icon={<ReloadOutlined />} 
                      onClick={loadTreeData}
                      loading={treeLoading}
                    >
                      刷新
                    </Button>
                  </Space>
                </div>
                
                <div style={{ flex: 1, minHeight: 0, overflow: 'auto' }}>
                  <Spin spinning={treeLoading}>
                    <Tree
                      treeData={treeData}
                      selectedKeys={selectedTreeKeys}
                      expandedKeys={expandedKeys}
                      onSelect={handleTreeSelect}
                      onExpand={handleTreeExpand}
                      showLine
                      showIcon
                      checkable={isAssigningResources}
                      checkedKeys={selectedResourcesForAssignment}
                      onCheck={(checkedKeys) => {
                        setSelectedResourcesForAssignment(
                          Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked || []
                        );
                      }}
                    />
                  </Spin>
                </div>
                
                {/* 资源分配选择按钮 */}
                {isAssigningResources && (
                  <div style={{ marginTop: 16, display: 'flex', gap: 8 }}>
                    <Button
                      type="primary"
                      onClick={handleOpenAssignmentModal}
                      disabled={selectedResourcesForAssignment.length === 0}
                    >
                      选择目标应用 ({selectedResourcesForAssignment.length})
                    </Button>
                    <Button
                      onClick={() => {
                        setIsAssigningResources(false);
                        setSelectedResourcesForAssignment([]);
                      }}
                    >
                      取消
                    </Button>
                  </div>
                )}
              </>
            ) : (
              /* 未选择应用时显示提示信息 */
              <div style={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column', 
                justifyContent: 'center', 
                alignItems: 'center',
                color: '#8c8c8c'
              }}>
                <AppstoreOutlined style={{ fontSize: 48, marginBottom: 16, opacity: 0.6 }} />
                <Text type="secondary" style={{ fontSize: 16, textAlign: 'center' }}>
                  请先选择应用<br />
                  <Text type="secondary" style={{ fontSize: 14 }}>
                    选择应用后将自动加载资源树
                  </Text>
                </Text>
              </div>
            )}
          </div>
        </Sider>
        {/* 新增资源弹窗 */}
        <Modal
          title="新增资源"
          open={isAddingResource}
          onOk={handleAddResource}
          onCancel={() => {
            setIsAddingResource(false);
            addResourceForm.resetFields();
          }}
          width={600}
        >
          <Form
            form={addResourceForm}
            layout="vertical"
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="资源名称"
                  name="name"
                  rules={[
                    { required: true, message: '请输入资源名称' },
                    { min: 3, max: 50, message: '资源名称必须是3-50个字符' },
                    { 
                      pattern: /^[a-zA-Z0-9_\-\u4e00-\u9fa5]{3,50}$/,
                      message: '资源名称只能包含字母、数字、下划线、中划线或中文'
                    }
                  ]}
                >
                  <Input placeholder="请输入资源名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="显示名称"
                  name="display_name"
                  rules={[
                    { required: true, message: '请输入显示名称' },
                    { min: 2, max: 100, message: '显示名称必须是2-100个字符' }
                  ]}
                >
                  <Input placeholder="请输入显示名称" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="资源类型"
                  name="resource_type"
                  rules={[{ required: true, message: '请选择资源类型' }]}
                >
                  <Select placeholder="请选择资源类型">
                    {resourceTypes.map(type => (
                      <Option key={type.value} value={type.value}>
                        {type.icon} {type.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="父节点"
                  name="parent_id"
                >
                  <TreeSelect
                    placeholder="请选择父节点"
                    allowClear
                    treeData={treeData}
                    treeDefaultExpandAll
                    showSearch
                    treeNodeFilterProp="title"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="路径"
                  name="path"
                  rules={[
                    {
                      pattern: /^\/.*$/,
                      message: '路径必须以 / 开头'
                    }
                  ]}
                >
                  <Input placeholder="请输入路径（必须以 / 开头）" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="图标"
                  name="icon"
                >
                  <Input placeholder="请输入图标" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="排序"
                  name="sort_order"
                >
                  <InputNumber min={0} placeholder="排序" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="公开访问"
                  name="is_public"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="可分配"
                  name="assignable"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="描述"
              name="description"
              rules={[
                { max: 255, message: '描述不能超过255个字符' }
              ]}
            >
              <TextArea rows={3} placeholder="请输入描述" />
            </Form.Item>
          </Form>
        </Modal>
        
        {/* 右侧Tab内容 */}
        <Content style={{ padding: '16px', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
          {selectedInternalAppId ? (
            <Tabs 
              activeKey={activeTab} 
              onChange={setActiveTab}
              style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
              tabBarStyle={{ flexShrink: 0 }}
              items={[
                {
                  key: 'basic',
                  label: (
                    <span>
                      <InfoCircleOutlined />
                      基本信息
                    </span>
                  ),
                  children: (
                    <div style={{ flex: 1, minHeight: 0, overflow: 'auto' }}>
                      {renderBasicInfoTab()}
                    </div>
                  ),
                },
                ...(
                  selectedResource?.resource_type !== 'api'
                    ? [
                        {
                          key: 'api',
                          label: (
                            <span>
                              <AppstoreOutlined />
                              功能列表
                            </span>
                          ),
                          children: (
                            <div style={{ flex: 1, minHeight: 0, overflow: 'auto' }}>
                              {renderApiListTab()}
                            </div>
                          ),
                        },
                      ]
                    : []
                ),
                {
                  key: 'permission',
                  label: (
                    <span>
                      <SecurityScanOutlined />
                      权限配置
                    </span>
                  ),
                  children: (
                    <div style={{ flex: 1, minHeight: 0, overflow: 'auto' }}>
                      {renderPermissionConfigTab()}
                    </div>
                  ),
                },
              ]}
            />
          ) : (
            /* 未选择应用时显示提示信息 */
            <div style={{ 
              height: '100%', 
              display: 'flex', 
              flexDirection: 'column', 
              justifyContent: 'center', 
              alignItems: 'center',
              color: '#8c8c8c'
            }}>
              <FileTextOutlined style={{ fontSize: 48, marginBottom: 16, opacity: 0.6 }} />
              <Text type="secondary" style={{ fontSize: 16, textAlign: 'center' }}>
                请先选择应用<br />
                <Text type="secondary" style={{ fontSize: 14 }}>
                  选择应用后将显示资源管理界面
                </Text>
              </Text>
            </div>
          )}
        </Content>
      </Layout>
      
      {/* 资源分配模态框 */}
      <Modal
        title="分配资源到应用"
        open={assignmentModalVisible}
        onOk={handleAssignResources}
        onCancel={() => {
          setAssignmentModalVisible(false);
          setSelectedTargetTenantId(null);
          setSelectedTargetAppId(null);
          setTargetApps([]);
        }}
        okText="确认分配"
        cancelText="取消"
        width={600}
        okButtonProps={{ 
          disabled: !selectedTargetTenantId || !selectedTargetAppId || selectedResourcesForAssignment.length === 0 
        }}
      >
        <div style={{ marginBottom: 16 }}>
          <Text>
            将选中的 <Text strong>{selectedResourcesForAssignment.length}</Text> 个资源分配给目标应用
          </Text>
        </div>
        
        <Form layout="vertical">
          <Form.Item 
            label="选择租户" 
            required
          >
            <Select
              placeholder="请先选择租户"
              value={selectedTargetTenantId}
              onChange={handleTargetTenantChange}
              loading={loadingTenants}
              showSearch
              optionFilterProp="children"
              style={{ width: '100%' }}
            >
              {tenants.map(tenant => (
                <Option key={tenant.id} value={tenant.id}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{tenant.tenant_name}</span>
                    <Tag color="green">{tenant.tenant_code}</Tag>
                  </div>
                </Option>
              ))}
            </Select>
            {tenants.length === 0 && !loadingTenants && (
              <div style={{ marginTop: 8, color: '#8c8c8c', fontSize: 12 }}>
                没有找到可用的租户
              </div>
            )}
          </Form.Item>
          
          <Form.Item 
            label="选择目标应用" 
            required
          >
            <Select
              placeholder={selectedTargetTenantId ? "请选择要分配到的目标应用" : "请先选择租户"}
              value={selectedTargetAppId}
              onChange={setSelectedTargetAppId}
              loading={loadingTargetApps}
              disabled={!selectedTargetTenantId}
              showSearch
              optionFilterProp="children"
              style={{ width: '100%' }}
            >
              {targetApps.map(app => (
                <Option key={app.internal_app_id} value={app.internal_app_id}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{app.app_name}</span>
                    <Tag color="blue">{app.app_type}</Tag>
                  </div>
                </Option>
              ))}
            </Select>
            {selectedTargetTenantId && targetApps.length === 0 && !loadingTargetApps && (
              <div style={{ marginTop: 8, color: '#8c8c8c', fontSize: 12 }}>
                该租户下没有找到可分配的目标应用
              </div>
            )}
          </Form.Item>
          
          {selectedTargetAppId && (
            <Form.Item label="目标应用信息">
              <Card size="small" style={{ backgroundColor: '#f9f9f9' }}>
                {(() => {
                  const targetApp = targetApps.find(app => app.internal_app_id === selectedTargetAppId);
                  return targetApp ? (
                    <div>
                      <div><Text strong>应用名称：</Text>{targetApp.app_name}</div>
                      <div><Text strong>应用类型：</Text>{targetApp.app_type}</div>
                      <div><Text strong>内部ID：</Text>{targetApp.internal_app_id}</div>
                      <div><Text strong>租户ID：</Text>{targetApp.tenant_id}</div>
                    </div>
                  ) : null;
                })()}
              </Card>
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default ResourcePage; 