# Go 代码稳定性 Code Review 强制检查标准

## 1. Goroutine 管理与泄漏防护

### 1.1 Goroutine 生命周期管理
- **强制要求**: 每个启动的 goroutine 必须有明确的退出机制
- **检查点**: 
  - 是否使用 context.Context 控制 goroutine 生命周期
  - 长期运行的 goroutine 是否监听 ctx.Done()
  - 是否使用 sync.WaitGroup 等待 goroutine 完成

```go
// ❌ 错误示例 - goroutine 泄漏
func badExample() {
    go func() {
        for {
            // 无退出条件的无限循环
            doWork()
        }
    }()
}

// ✅ 正确示例
func goodExample(ctx context.Context) {
    go func() {
        for {
            select {
            case <-ctx.Done():
                return
            default:
                doWork()
            }
        }
    }()
}
```

### 1.2 Channel 关闭与资源清理
- **强制要求**: 发送方负责关闭 channel，接收方检查 channel 状态
- **检查点**:
  - channel 是否在适当时机关闭
  - 是否存在向已关闭 channel 发送数据的风险
  - 是否正确处理 channel 的接收状态

## 2. 超时与熔断机制

### 2.1 HTTP 客户端超时配置
- **强制要求**: 所有 HTTP 客户端必须设置合理的超时时间
- **检查点**:
  - 是否设置 ConnectTimeout、ReadTimeout、WriteTimeout
  - 是否使用 context.WithTimeout 控制请求超时
  - 超时时间是否合理（避免过长导致雪崩）

```go
// ❌ 错误示例 - 无超时配置
client := &http.Client{}

// ✅ 正确示例
client := &http.Client{
    Timeout: 30 * time.Second,
    Transport: &http.Transport{
        DialContext: (&net.Dialer{
            Timeout:   5 * time.Second,
            KeepAlive: 30 * time.Second,
        }).DialContext,
        TLSHandshakeTimeout:   10 * time.Second,
        ResponseHeaderTimeout: 10 * time.Second,
    },
}
```

### 2.2 数据库连接超时
- **强制要求**: 数据库操作必须设置超时
- **检查点**:
  - 是否使用 context.WithTimeout
  - 连接池配置是否合理
  - 是否处理超时错误

## 3. 资源泄漏防护

### 3.1 文件句柄管理
- **强制要求**: 所有打开的文件必须在 defer 中关闭
- **检查点**:
  - 是否使用 defer file.Close()
  - 是否检查 Close() 的错误返回值
  - 是否在错误路径中也能正确关闭资源

```go
// ✅ 正确示例
func readFile(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer func() {
        if closeErr := file.Close(); closeErr != nil {
            log.Printf("Failed to close file: %v", closeErr)
        }
    }()
    
    // 文件操作...
    return nil
}
```

### 3.2 HTTP Response Body 关闭
- **强制要求**: HTTP 响应的 Body 必须关闭
- **检查点**:
  - 是否在 defer 中关闭 resp.Body
  - 是否在错误情况下也关闭 Body

### 3.3 数据库连接管理
- **强制要求**: 数据库连接、事务、语句必须正确关闭
- **检查点**:
  - 是否使用连接池
  - 事务是否正确提交或回滚
  - prepared statement 是否关闭

## 4. Panic 处理与恢复

### 4.1 Panic 恢复机制
- **强制要求**: 关键路径必须有 panic 恢复机制
- **检查点**:
  - HTTP handler 是否有 recover 中间件
  - 长期运行的 goroutine 是否有 recover
  - 是否记录 panic 信息用于调试

```go
// ✅ 正确示例 - HTTP 中间件
func recoverMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        defer func() {
            if err := recover(); err != nil {
                log.Printf("Panic recovered: %v\n%s", err, debug.Stack())
                http.Error(w, "Internal Server Error", 500)
            }
        }()
        next.ServeHTTP(w, r)
    })
}
```

### 4.2 避免 Panic 级联
- **强制要求**: 避免在 defer 或 recover 中再次 panic
- **检查点**:
  - defer 函数是否可能 panic
  - recover 处理逻辑是否安全

## 5. 并发安全

### 5.1 共享状态保护
- **强制要求**: 共享状态必须使用适当的同步机制
- **检查点**:
  - 是否使用 sync.Mutex、sync.RWMutex 保护共享数据
  - 是否存在数据竞争风险
  - 是否正确使用 atomic 包

### 5.2 Map 并发安全
- **强制要求**: 并发访问的 map 必须使用 sync.Map 或加锁保护
- **检查点**:
  - 普通 map 是否在多 goroutine 中使用
  - 是否使用 sync.Map 或 mutex 保护

## 6. 内存管理

### 6.1 内存泄漏防护
- **强制要求**: 避免循环引用和大对象长期持有
- **检查点**:
  - 是否存在循环引用
  - slice 是否正确重置容量
  - 定时器是否正确停止

```go
// ❌ 错误示例 - slice 内存泄漏
func badSliceUsage(data []byte) []byte {
    return data[100:200] // 持有整个 data 的引用
}

// ✅ 正确示例
func goodSliceUsage(data []byte) []byte {
    result := make([]byte, 100)
    copy(result, data[100:200])
    return result
}
```

### 6.2 定时器管理
- **强制要求**: time.Timer 和 time.Ticker 必须正确停止
- **检查点**:
  - 是否调用 Stop() 方法
  - 是否在 defer 中停止定时器

## 7. 错误处理

### 7.1 错误传播
- **强制要求**: 错误必须正确处理，不能忽略
- **检查点**:
  - 是否检查所有函数的错误返回值
  - 错误是否包含足够的上下文信息
  - 是否使用 errors.Wrap 添加上下文

### 7.2 错误日志
- **强制要求**: 关键错误必须记录日志
- **检查点**:
  - 是否记录错误堆栈
  - 日志级别是否合适
  - 是否包含请求ID等追踪信息

## 8. 可观测性要求

### 8.1 日志规范
- **强制要求**: 关键操作必须有日志记录
- **检查点**:
  - 是否记录请求开始和结束
  - 是否记录关键业务操作
  - 日志格式是否结构化

### 8.2 指标监控
- **强制要求**: 关键指标必须暴露监控
- **检查点**:
  - 是否记录请求延迟、错误率
  - 是否监控资源使用情况
  - 是否有业务指标监控

## 9. 配置管理

### 9.1 配置验证
- **强制要求**: 启动时必须验证配置完整性
- **检查点**:
  - 是否验证必需配置项
  - 配置值是否在合理范围内
  - 是否有默认值处理

### 9.2 优雅关闭
- **强制要求**: 服务必须支持优雅关闭
- **检查点**:
  - 是否监听系统信号
  - 是否等待正在处理的请求完成
  - 是否清理资源

```go
// ✅ 优雅关闭示例
func gracefulShutdown(server *http.Server) {
    c := make(chan os.Signal, 1)
    signal.Notify(c, os.Interrupt, syscall.SIGTERM)
    
    <-c
    log.Println("Shutting down server...")
    
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    if err := server.Shutdown(ctx); err != nil {
        log.Printf("Server shutdown error: %v", err)
    }
}
```

## Code Review 检查清单

### 必检项目
- [ ] Goroutine 是否有退出机制
- [ ] HTTP 客户端是否设置超时
- [ ] 文件/连接是否在 defer 中关闭
- [ ] 是否有 panic 恢复机制
- [ ] 共享状态是否有并发保护
- [ ] 错误是否正确处理
- [ ] 是否有适当的日志记录
- [ ] 配置是否验证
- [ ] 是否支持优雅关闭

### 工具辅助检查
- 使用 `go vet` 检查常见问题
- 使用 `go race` 检查数据竞争
- 使用 `golangci-lint` 进行静态分析
- 使用 `pprof` 分析内存和CPU使用

---

**注意**: 本文档中的所有要求都是强制性的，任何违反这些标准的代码都不应通过 Code Review。
